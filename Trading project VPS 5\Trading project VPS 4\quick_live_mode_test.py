#!/usr/bin/env python3
"""
Quick Live Mode Test
===================
"""

import requests
import json

def test_live_mode():
    base_url = "http://localhost:5000"
    
    print("🧪 QUICK LIVE MODE TEST")
    print("=" * 30)
    
    # Test TESTNET mode
    print("\n1️⃣ Testing TESTNET live mode...")
    try:
        response = requests.post(f"{base_url}/api/toggle_live_mode", 
                               json={
                                   'live_mode': True,
                                   'testnet': True,
                                   'use_margin': False
                               }, 
                               timeout=10)
        print(f"Status Code: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"Response: {json.dumps(data, indent=2)}")
        else:
            print(f"Error: {response.text}")
    except Exception as e:
        print(f"❌ Error: {e}")
    
    # Test back to simulation
    print("\n2️⃣ Testing back to simulation...")
    try:
        response = requests.post(f"{base_url}/api/toggle_live_mode", 
                               json={'live_mode': False}, 
                               timeout=10)
        print(f"Status Code: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"Response: {json.dumps(data, indent=2)}")
        else:
            print(f"Error: {response.text}")
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    test_live_mode()
