# Conservative Elite Trading System - Docker Ignore File

# Environment files
.env
.env.local
.env.*.local

# Python cache
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Virtual environments
venv/
env/
ENV/

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# OS files
.DS_Store
Thumbs.db

# Logs (will be mounted as volume)
logs/*.log
*.log

# Temporary files
tmp/
temp/
*.tmp

# Git
.git/
.gitignore

# Documentation
README.md
*.md
docs/

# Development files
test_*.py
*_test.py
tests/

# Backup files
*.backup
*.bak
backups/

# Large model files (will be copied separately if needed)
models/*.pth
models/*.pkl
models/*.joblib

# Database files (will be mounted as volume)
*.db
*.db-shm
*.db-wal

# Cache directories (will be mounted as volume)
cache/
.cache/

# Development scripts
dev_*.py
debug_*.py

# Jupyter notebooks
*.ipynb
.ipynb_checkpoints/

# Coverage reports
htmlcov/
.coverage
.coverage.*

# Deployment files
deploy-*.sh
docker-compose.override.yml
