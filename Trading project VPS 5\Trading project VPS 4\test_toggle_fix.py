#!/usr/bin/env python3
"""
Test Toggle Live Mode Fix
=========================
"""

import requests
import json

def test_toggle_endpoints():
    base_url = "http://localhost:5000"
    
    print("🧪 TESTING TOGGLE LIVE MODE FIX")
    print("=" * 40)
    
    # Test 1: Check current status
    print("\n1️⃣ Current status...")
    try:
        response = requests.get(f"{base_url}/api/trading_status", timeout=5)
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Current live mode: {data.get('is_live_mode', False)}")
            print(f"✅ Binance connected: {data.get('binance_connected', False)}")
        else:
            print(f"❌ Status check failed: {response.status_code}")
    except Exception as e:
        print(f"❌ Status check error: {e}")
    
    # Test 2: Test legacy /api/toggle_mode endpoint
    print("\n2️⃣ Testing legacy /api/toggle_mode endpoint...")
    try:
        response = requests.post(f"{base_url}/api/toggle_mode", 
                               json={'live_mode': True}, 
                               timeout=15)
        print(f"Status Code: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Legacy endpoint works!")
            print(f"Response: {json.dumps(data, indent=2)}")
        else:
            print(f"❌ Legacy endpoint failed: {response.text}")
    except Exception as e:
        print(f"❌ Legacy endpoint error: {e}")
    
    # Test 3: Test new /api/toggle_live_mode endpoint
    print("\n3️⃣ Testing new /api/toggle_live_mode endpoint...")
    try:
        response = requests.post(f"{base_url}/api/toggle_live_mode", 
                               json={'live_mode': True}, 
                               timeout=15)
        print(f"Status Code: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ New endpoint works!")
            print(f"Response: {json.dumps(data, indent=2)}")
        else:
            print(f"❌ New endpoint failed: {response.text}")
    except Exception as e:
        print(f"❌ New endpoint error: {e}")
    
    # Test 4: Check final status
    print("\n4️⃣ Final status check...")
    try:
        response = requests.get(f"{base_url}/api/trading_status", timeout=5)
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Final live mode: {data.get('is_live_mode', False)}")
            print(f"✅ Final Binance connected: {data.get('binance_connected', False)}")
        else:
            print(f"❌ Final status check failed: {response.status_code}")
    except Exception as e:
        print(f"❌ Final status check error: {e}")
    
    print("\n" + "=" * 40)
    print("🎯 TOGGLE TEST COMPLETE!")
    print("🌐 Dashboard: http://localhost:5000")
    print("💡 Try the 'Switch to Live Mode' button now!")
    print("=" * 40)

if __name__ == "__main__":
    test_toggle_endpoints()
