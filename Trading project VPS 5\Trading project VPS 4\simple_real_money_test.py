#!/usr/bin/env python3
"""
Simple Real Money Mode Test
===========================
"""

import requests
import json

def test_real_money_mode():
    base_url = "http://localhost:5000"
    
    print("🧪 SIMPLE REAL MONEY MODE TEST")
    print("=" * 40)
    
    # Test 1: Activate real money mode
    print("\n1️⃣ Testing real money mode activation...")
    try:
        response = requests.post(f"{base_url}/api/toggle_live_mode", 
                               json={
                                   'live_mode': True,
                                   'use_margin': False
                               }, 
                               timeout=15)
        
        print(f"Status Code: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"Response: {json.dumps(data, indent=2)}")
            
            if data.get('status') == 'success':
                print("✅ Real money mode activated successfully!")
                print(f"   Live Mode: {data.get('live_mode', False)}")
                print(f"   Testnet: {data.get('testnet', 'unknown')}")
                print(f"   Connected: {data.get('connected', False)}")
            else:
                print(f"❌ Activation failed: {data.get('message', 'Unknown error')}")
        else:
            print(f"❌ HTTP Error: {response.text}")
    except Exception as e:
        print(f"❌ Request failed: {e}")
    
    # Test 2: Check Binance status
    print("\n2️⃣ Checking Binance connection status...")
    try:
        response = requests.get(f"{base_url}/api/binance_status", timeout=10)
        print(f"Status Code: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"Response: {json.dumps(data, indent=2)}")
            
            connected = data.get('connected', False)
            testnet = data.get('testnet', True)
            
            if connected and not testnet:
                print("✅ Real money connection successful!")
            elif connected and testnet:
                print("⚠️ Connected but still in testnet mode")
            else:
                print("❌ Not connected to Binance")
        else:
            print(f"❌ HTTP Error: {response.text}")
    except Exception as e:
        print(f"❌ Request failed: {e}")
    
    # Test 3: Check trading status
    print("\n3️⃣ Checking trading status...")
    try:
        response = requests.get(f"{base_url}/api/trading_status", timeout=5)
        print(f"Status Code: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            live_mode = data.get('is_live_mode', False)
            binance_connected = data.get('binance_connected', False)
            
            print(f"Live Mode: {live_mode}")
            print(f"Binance Connected: {binance_connected}")
            print(f"Current Price: ${data.get('current_price', 0):,.2f}")
            
            if live_mode and binance_connected:
                print("✅ System is in real money trading mode!")
            else:
                print("⚠️ System not fully in real money mode")
        else:
            print(f"❌ HTTP Error: {response.text}")
    except Exception as e:
        print(f"❌ Request failed: {e}")
    
    print("\n" + "=" * 40)
    print("🎯 TEST COMPLETE!")
    print("🌐 Dashboard: http://localhost:5000")
    print("=" * 40)

if __name__ == "__main__":
    test_real_money_mode()
