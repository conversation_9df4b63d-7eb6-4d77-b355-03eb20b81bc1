<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">
    <title>Bitcoin Freedom Live Trading</title>
    <!-- CONSERVATIVE ELITE CACHE BUSTER: 20250105_CONSERVATIVE_ELITE_932_UPDATE -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            color: white;
            min-height: 100vh;
            overflow-x: hidden;
        }

        .header {
            text-align: center;
            padding: 30px 20px;
            background: rgba(0, 0, 0, 0.3);
            backdrop-filter: blur(10px);
            border-bottom: 2px solid rgba(0, 212, 170, 0.3);
        }

        .header h1 {
            font-size: 3rem;
            margin-bottom: 10px;
            background: linear-gradient(45deg, #00d4aa, #00ff88);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-shadow: 0 0 30px rgba(0, 212, 170, 0.5);
        }

        .header .subtitle {
            font-size: 1.1rem;
            opacity: 0.9;
            margin-bottom: 20px;
        }

        .status-indicators {
            display: flex;
            justify-content: center;
            gap: 20px;
            flex-wrap: wrap;
        }

        .status-indicator {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: 500;
        }

        .status-running {
            background: rgba(0, 255, 136, 0.2);
            border: 1px solid #00ff88;
            color: #00ff88;
        }

        .status-stopped {
            background: rgba(255, 107, 107, 0.2);
            border: 1px solid #ff6b6b;
            color: #ff6b6b;
        }

        .status-live {
            background: rgba(255, 165, 0, 0.2);
            border: 1px solid #ffa500;
            color: #ffa500;
        }

        .status-sim {
            background: rgba(0, 212, 170, 0.2);
            border: 1px solid #00d4aa;
            color: #00d4aa;
        }

        .pulse {
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.7; }
            100% { opacity: 1; }
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .status-cog {
            position: fixed;
            bottom: 20px;
            left: 20px;
            width: 40px;
            height: 40px;
            background: rgba(0, 0, 0, 0.7);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            z-index: 1000;
            border: 2px solid rgba(255, 255, 255, 0.2);
        }

        .status-cog:hover {
            background: rgba(0, 0, 0, 0.9);
            transform: scale(1.1);
            border-color: #00d4aa;
        }

        .status-cog i {
            color: #ffffff;
            font-size: 18px;
            transition: transform 0.3s ease;
        }

        .status-cog:hover i {
            transform: rotate(90deg);
            color: #00d4aa;
        }

        .status-popup {
            position: fixed;
            bottom: 70px;
            left: 20px;
            background: rgba(0, 0, 0, 0.95);
            border: 2px solid #00d4aa;
            border-radius: 10px;
            padding: 15px;
            min-width: 250px;
            max-width: 400px;
            font-family: monospace;
            font-size: 12px;
            color: #ffffff;
            z-index: 999;
            display: none;
            backdrop-filter: blur(10px);
        }

        .status-popup h4 {
            color: #00d4aa;
            margin: 0 0 10px 0;
            font-size: 14px;
        }

        .status-popup .status-item {
            margin: 5px 0;
            display: flex;
            justify-content: space-between;
        }

        .status-popup .status-value {
            color: #00ff88;
        }

        .status-popup .status-error {
            color: #ff4444;
        }

        .status-popup .status-warning {
            color: #ffa500;
        }

        .controls {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin: 30px 0;
            flex-wrap: wrap;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .btn-start {
            background: linear-gradient(45deg, #00ff88, #00d4aa);
            color: #000;
        }

        .btn-start:hover:not(:disabled) {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 255, 136, 0.3);
        }

        .btn-stop {
            background: linear-gradient(45deg, #ff6b6b, #ff4444);
            color: white;
        }

        .btn-stop:hover:not(:disabled) {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(255, 107, 107, 0.3);
        }

        .btn-toggle {
            background: linear-gradient(45deg, #ffa500, #ff8c00);
            color: white;
        }

        .btn-toggle:hover:not(:disabled) {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(255, 165, 0, 0.3);
        }

        .price-display {
            text-align: center;
            margin: 40px 0;
            padding: 30px;
            background: rgba(0, 0, 0, 0.2);
            border-radius: 15px;
            border: 2px solid rgba(0, 212, 170, 0.3);
        }

        .price-display h2 {
            font-size: 3.5rem;
            color: #00ff88;
            margin-bottom: 10px;
            text-shadow: 0 0 20px rgba(0, 255, 136, 0.5);
        }

        .price-display p {
            font-size: 1.2rem;
            opacity: 0.8;
        }

        .dashboard-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 30px;
            margin: 30px 0;
        }

        .dashboard-section {
            background: rgba(0, 0, 0, 0.2);
            border-radius: 15px;
            padding: 25px;
            border: 2px solid rgba(0, 212, 170, 0.3);
            backdrop-filter: blur(10px);
        }

        .dashboard-section h3 {
            color: #00d4aa;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
            font-size: 1.3rem;
        }

        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
        }

        .metric-item {
            text-align: center;
            padding: 15px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .metric-label {
            font-size: 0.9rem;
            opacity: 0.8;
            margin-bottom: 5px;
        }

        .metric-value {
            font-size: 1.4rem;
            font-weight: 600;
            color: #ffffff;
        }

        .metric-value.profit {
            color: #00ff88;
        }

        .metric-value.loss {
            color: #ff6b6b;
        }

        .no-trades {
            text-align: center;
            padding: 40px;
            opacity: 0.6;
            font-style: italic;
        }

        @media (max-width: 768px) {
            .header h1 {
                font-size: 2rem;
            }
            
            .price-display h2 {
                font-size: 2.5rem;
            }
            
            .dashboard-grid {
                grid-template-columns: 1fr;
            }
            
            .controls {
                flex-direction: column;
                align-items: center;
            }
            
            .btn {
                width: 200px;
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1><i class="fas fa-bitcoin"></i> Bitcoin Freedom Live Trading</h1>
        <div class="subtitle" id="headerSubtitle">
            Live Trading System
        </div>

        <div class="status-indicators">
            <div class="status-indicator status-stopped" id="tradingStatus">
                <i class="fas fa-circle"></i><span>Trading Stopped</span>
            </div>
            <div class="status-indicator status-sim" id="modeStatus">
                <i class="fas fa-flask"></i><span>Simulation Mode</span>
            </div>
            <div class="status-indicator" style="background: rgba(255, 215, 0, 0.2); border: 1px solid #ffd700; color: #ffd700;">
                <i class="fas fa-lock"></i><span>Model Locked</span>
            </div>
        </div>
    </div>

    <div class="container">
        <div class="controls">
            <button class="btn btn-start" id="startBtn" onclick="startTrading()">
                <i class="fas fa-play"></i> Start Trading
            </button>
            <button class="btn btn-stop" id="stopBtn" onclick="stopTrading()" disabled>
                <i class="fas fa-stop"></i> Stop Trading
            </button>
            <button class="btn btn-toggle" id="toggleBtn" onclick="toggleMode()">
                <i class="fas fa-exchange-alt"></i> Switch to Live Mode
            </button>
        </div>

        <div class="price-display">
            <h2 id="currentPrice">$105,341.62</h2>
            <p>Bitcoin Price (Real-Time)</p>
        </div>



        <div class="dashboard-grid">
            <div class="dashboard-section">
                <h3><i class="fas fa-chart-line"></i> Trading Performance</h3>
                <div class="metrics-grid">
                    <div class="metric-item">
                        <div class="metric-label">Equity</div>
                        <div class="metric-value" id="equity">$300.00</div>
                    </div>
                    <div class="metric-item">
                        <div class="metric-label">Total P&L</div>
                        <div class="metric-value profit" id="totalPnl">$0.00</div>
                    </div>
                    <div class="metric-item">
                        <div class="metric-label">Win Rate</div>
                        <div class="metric-value" id="winRate">0%</div>
                    </div>
                    <div class="metric-item">
                        <div class="metric-label">Daily P&L</div>
                        <div class="metric-value profit" id="dailyPnl">$0.00</div>
                    </div>
                </div>
            </div>

            <div class="dashboard-section">
                <h3><i class="fas fa-cogs"></i> System Status</h3>
                <div class="metrics-grid">
                    <div class="metric-item">
                        <div class="metric-label">Open Positions</div>
                        <div class="metric-value" id="openPositions">0</div>
                    </div>
                    <div class="metric-item">
                        <div class="metric-label">Daily Trades</div>
                        <div class="metric-value" id="dailyTrades">0</div>
                    </div>
                    <div class="metric-item">
                        <div class="metric-label">Total Trades</div>
                        <div class="metric-value" id="totalTrades">0</div>
                    </div>
                    <div class="metric-item">
                        <div class="metric-label">Conservative Elite Score</div>
                        <div class="metric-value" id="compositeScore">79.1%</div>
                    </div>
                </div>
            </div>
        </div>

        <div class="dashboard-section">
            <h3><i class="fas fa-history"></i> Recent Trades</h3>
            <div id="tradesContainer">
                <div class="no-trades">No trades yet - start trading to see results</div>
            </div>
        </div>
    </div>

    <!-- Status Cog -->
    <div class="status-cog" onclick="toggleStatusPopup()">
        <i class="fas fa-cog"></i>
    </div>

    <!-- Status Popup -->
    <div class="status-popup" id="statusPopup">
        <h4><i class="fas fa-info-circle"></i> System Status</h4>
        <div id="statusContent">Loading...</div>
    </div>

    <script>
        // Global state
        let state = {
            isRunning: false,
            isLiveMode: false,
            updateInterval: null
        };

        // API functions
        async function apiCall(endpoint, options = {}) {
            try {
                console.log(`🌐 API Call: ${endpoint}`);
                const response = await fetch(endpoint, {
                    headers: {
                        'Content-Type': 'application/json',
                        ...options.headers
                    },
                    ...options
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const data = await response.json();
                console.log(`✅ API Response: ${endpoint}`, data);
                return data;
            } catch (error) {
                console.error(`❌ API Error: ${endpoint}`, error);
                throw error;
            }
        }

        async function updateDashboard() {
            try {
                console.log('🔄 Updating dashboard...');
                const data = await apiCall('/api/trading_status');

                // Update state
                state.isRunning = data.is_running;
                state.isLiveMode = data.is_live_mode;

                // Update header - clean model type
                let cleanModelType = data.model_info.model_type
                    .replace(/TCN-CNN-PPO/g, 'AI Model')
                    .replace(/Ensemble/g, '')
                    .replace(/AI Model AI/g, 'AI Model')
                    .replace(/\s+/g, ' ')
                    .trim();

                if (cleanModelType === 'AI Model AI' || cleanModelType === '') {
                    cleanModelType = 'AI Model';
                }

                document.getElementById('headerSubtitle').textContent =
                    `Live Trading System`;

                // Update price
                document.getElementById('currentPrice').textContent = '$' + data.current_price.toLocaleString();

                // Update metrics
                document.getElementById('equity').textContent = '$' + data.performance.equity.toFixed(2);
                document.getElementById('totalPnl').textContent = '$' + data.performance.total_profit.toFixed(2);
                document.getElementById('winRate').textContent = data.performance.win_rate + '%';
                document.getElementById('dailyPnl').textContent = '$' + data.performance.daily_pnl.toFixed(2);
                document.getElementById('openPositions').textContent = data.performance.open_positions;
                document.getElementById('dailyTrades').textContent = data.performance.daily_trades;
                document.getElementById('totalTrades').textContent = data.performance.total_trades;
                document.getElementById('compositeScore').textContent = '79.1%';

                // Update colors
                updateColors();
                updateButtons();
                updateStatus();

                // Update trades
                await updateTrades();

            } catch (error) {
                console.error('❌ Dashboard update failed:', error);
            }
        }

        async function updateTrades() {
            try {
                const trades = await apiCall('/api/recent_trades');
                const container = document.getElementById('tradesContainer');

                if (trades.length === 0) {
                    container.innerHTML = '<div class="no-trades">No trades yet - start trading to see results</div>';
                    return;
                }

                // Group trades by trade_id to show only one entry per trade (latest status)
                const uniqueTrades = {};
                trades.forEach(trade => {
                    const tradeId = trade.trade_id;
                    if (!uniqueTrades[tradeId] || new Date(trade.entry_time) > new Date(uniqueTrades[tradeId].entry_time)) {
                        uniqueTrades[tradeId] = trade;
                    }
                });

                // Convert back to array and sort by entry time (most recent first)
                const consolidatedTrades = Object.values(uniqueTrades)
                    .sort((a, b) => new Date(b.entry_time) - new Date(a.entry_time))
                    .slice(0, 10); // Show last 10 unique trades

                // Calculate running balance
                let runningBalance = 300.0; // Starting balance
                const tradesWithBalance = consolidatedTrades.map((trade, index) => {
                    if (trade.status === 'CLOSED' && trade.realized_pnl) {
                        runningBalance += trade.realized_pnl;
                    }
                    return { ...trade, balance_after: runningBalance };
                });

                container.innerHTML = tradesWithBalance.map((trade, index) => {
                    const isOpen = trade.status === 'OPEN' || !trade.exit_price;
                    const pnlAmount = isOpen ? (trade.unrealized_pnl || trade.pnl || 0) : (trade.realized_pnl || trade.pnl || 0);
                    const pnlPercent = trade.profit_percentage || 0;

                    // Format dates and times
                    const entryDate = new Date(trade.entry_time).toLocaleDateString();
                    const entryTime = new Date(trade.entry_time).toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});
                    const exitDate = trade.exit_time ? new Date(trade.exit_time).toLocaleDateString() : null;
                    const exitTime = trade.exit_time ? new Date(trade.exit_time).toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'}) : null;

                    // Calculate trade amount in USD
                    const tradeAmountUSD = (trade.position_usd || (trade.position_size * trade.entry_price)) || 0;

                    // Duration calculation
                    let duration = 'Open';
                    if (trade.exit_time) {
                        const durationMs = new Date(trade.exit_time) - new Date(trade.entry_time);
                        const hours = Math.floor(durationMs / (1000 * 60 * 60));
                        const minutes = Math.floor((durationMs % (1000 * 60 * 60)) / (1000 * 60));
                        duration = hours > 0 ? `${hours}h ${minutes}m` : `${minutes}m`;
                    }

                    return `
                        <div style="background: rgba(0,0,0,0.3); padding: 18px; margin: 12px 0; border-radius: 10px; border-left: 5px solid ${trade.direction === 'BUY' ? '#00ff88' : '#ff6b6b'}; box-shadow: 0 4px 8px rgba(0,0,0,0.2); ${isOpen ? 'border: 1px solid rgba(255,165,0,0.3);' : ''}">
                            <!-- Trade Header -->
                            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 12px;">
                                <div style="display: flex; align-items: center; gap: 12px;">
                                    <strong style="color: ${trade.direction === 'BUY' ? '#00ff88' : '#ff6b6b'}; font-size: 1.1rem;">
                                        ${trade.direction}
                                    </strong>
                                    <span style="background: ${isOpen ? 'rgba(255,165,0,0.2)' : (pnlAmount >= 0 ? 'rgba(0,255,136,0.2)' : 'rgba(255,107,107,0.2)')};
                                                 color: ${isOpen ? '#ffa500' : (pnlAmount >= 0 ? '#00ff88' : '#ff6b6b')};
                                                 padding: 4px 8px; border-radius: 4px; font-size: 0.8rem; font-weight: bold;">
                                        ${isOpen ? '🔄 ACTIVE' : (pnlAmount >= 0 ? '✅ PROFIT' : '❌ LOSS')}
                                    </span>
                                    ${isOpen ? '<span style="color: #ffa500; font-size: 0.8rem; opacity: 0.8;">(Updates Live)</span>' : '<span style="color: #888; font-size: 0.8rem;">Final Result</span>'}
                                </div>
                                <div style="text-align: right;">
                                    <div style="color: ${pnlAmount >= 0 ? '#00ff88' : '#ff4444'}; font-weight: bold; font-size: 1.1rem;">
                                        ${pnlAmount >= 0 ? '+' : ''}$${Math.abs(pnlAmount).toFixed(2)}
                                    </div>
                                    <div style="color: ${pnlPercent >= 0 ? '#00ff88' : '#ff4444'}; font-size: 0.9rem;">
                                        (${pnlPercent >= 0 ? '+' : ''}${pnlPercent.toFixed(2)}%)
                                    </div>
                                </div>
                            </div>

                            <!-- Trade Details Grid -->
                            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin-bottom: 12px;">
                                <!-- Entry Details -->
                                <div style="background: rgba(255,255,255,0.05); padding: 12px; border-radius: 6px;">
                                    <h5 style="margin: 0 0 8px 0; color: #00d4aa; font-size: 0.9rem;">📈 ENTRY</h5>
                                    <div style="font-size: 0.85rem; line-height: 1.4;">
                                        <div><strong>Date:</strong> ${entryDate}</div>
                                        <div><strong>Time:</strong> ${entryTime}</div>
                                        <div><strong>Price:</strong> $${trade.entry_price.toLocaleString()}</div>
                                        <div><strong>Amount:</strong> $${tradeAmountUSD.toFixed(2)}</div>
                                        <div><strong>Size:</strong> ${(trade.position_size || trade.quantity || 0).toFixed(6)} BTC</div>
                                    </div>
                                </div>

                                <!-- Exit Details -->
                                <div style="background: rgba(255,255,255,0.05); padding: 12px; border-radius: 6px;">
                                    <h5 style="margin: 0 0 8px 0; color: ${isOpen ? '#ffa500' : '#00d4aa'}; font-size: 0.9rem;">
                                        ${isOpen ? '⏳ LIVE STATUS' : '🎯 FINAL EXIT'}
                                    </h5>
                                    <div style="font-size: 0.85rem; line-height: 1.4;">
                                        <div><strong>Date:</strong> ${exitDate || (isOpen ? 'Live' : 'N/A')}</div>
                                        <div><strong>Time:</strong> ${exitTime || (isOpen ? 'Live' : 'N/A')}</div>
                                        <div><strong>Price:</strong> ${trade.exit_price ? '$' + trade.exit_price.toLocaleString() : '$' + (trade.current_price || trade.entry_price).toLocaleString()}</div>
                                        <div><strong>Duration:</strong> ${duration}</div>
                                        <div><strong>Status:</strong> <span style="color: ${isOpen ? '#ffa500' : '#00ff88'}">${isOpen ? '🔄 Live Updates' : '✅ Completed'}</span></div>
                                    </div>
                                </div>
                            </div>

                            <!-- Financial Summary -->
                            <div style="background: rgba(255,255,255,0.05); padding: 12px; border-radius: 6px; margin-bottom: 8px;">
                                <h5 style="margin: 0 0 8px 0; color: #00d4aa; font-size: 0.9rem;">💰 FINANCIAL SUMMARY</h5>
                                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(120px, 1fr)); gap: 10px; font-size: 0.85rem;">
                                    <div><strong>Risk Amount:</strong> $${(trade.risk_amount || 10).toFixed(2)}</div>
                                    <div><strong>Target Profit:</strong> $${(trade.target_profit || 25).toFixed(2)}</div>
                                    <div><strong>P&L Amount:</strong> <span style="color: ${pnlAmount >= 0 ? '#00ff88' : '#ff4444'}">${pnlAmount >= 0 ? '+' : ''}$${Math.abs(pnlAmount).toFixed(2)}</span></div>
                                    <div><strong>Balance After:</strong> <span style="color: #00d4aa">$${trade.balance_after.toFixed(2)}</span></div>
                                </div>
                            </div>

                            <!-- Trade Metadata -->
                            <div style="display: flex; justify-content: space-between; align-items: center; font-size: 0.8rem; opacity: 0.7;">
                                <div>
                                    <span>ID: ${trade.trade_id || 'N/A'}</span>
                                    ${trade.confidence ? ` | Confidence: ${trade.confidence.toFixed(1)}%` : ''}
                                </div>
                                <div>
                                    ${trade.account_type || 'Spot'} ${trade.leverage && trade.leverage > 1 ? `| ${trade.leverage}x` : ''}
                                </div>
                            </div>
                        </div>
                    `;
                }).join('');

            } catch (error) {
                console.error('❌ Trades update failed:', error);
            }
        }

        function updateColors() {
            const totalPnl = parseFloat(document.getElementById('totalPnl').textContent.replace('$', ''));
            const dailyPnl = parseFloat(document.getElementById('dailyPnl').textContent.replace('$', ''));

            document.getElementById('totalPnl').className = 'metric-value ' + (totalPnl >= 0 ? 'profit' : 'loss');
            document.getElementById('dailyPnl').className = 'metric-value ' + (dailyPnl >= 0 ? 'profit' : 'loss');
        }

        function updateButtons() {
            document.getElementById('startBtn').disabled = state.isRunning;
            document.getElementById('stopBtn').disabled = !state.isRunning;
            document.getElementById('toggleBtn').disabled = state.isRunning;
            document.getElementById('toggleBtn').innerHTML = state.isLiveMode ?
                '<i class="fas fa-exchange-alt"></i> Switch to Simulation' :
                '<i class="fas fa-exchange-alt"></i> Switch to Live Mode';
        }

        function updateStatus() {
            const tradingStatus = document.getElementById('tradingStatus');
            const modeStatus = document.getElementById('modeStatus');

            if (state.isRunning) {
                tradingStatus.className = 'status-indicator status-running pulse';
                tradingStatus.innerHTML = '<i class="fas fa-circle"></i><span>Trading Active</span>';
            } else {
                tradingStatus.className = 'status-indicator status-stopped';
                tradingStatus.innerHTML = '<i class="fas fa-circle"></i><span>Trading Stopped</span>';
            }

            if (state.isLiveMode) {
                modeStatus.className = 'status-indicator status-live pulse';
                modeStatus.innerHTML = '<i class="fas fa-exclamation-triangle"></i><span>LIVE MODE</span>';
            } else {
                modeStatus.className = 'status-indicator status-sim';
                modeStatus.innerHTML = '<i class="fas fa-flask"></i><span>Simulation Mode</span>';
            }
        }

        // Control functions
        async function startTrading() {
            try {
                await apiCall('/api/start_trading', { method: 'POST' });
                await updateDashboard();
            } catch (error) {
                alert('Failed to start trading: ' + error.message);
            }
        }

        async function stopTrading() {
            try {
                await apiCall('/api/stop_trading', { method: 'POST' });
                await updateDashboard();
            } catch (error) {
                alert('Failed to stop trading: ' + error.message);
            }
        }

        async function toggleMode() {
            try {
                const newLiveMode = !state.isLiveMode;

                // Enhanced confirmation for real money trading
                if (newLiveMode) {
                    const userChoice = prompt(`
🚨 LIVE TRADING MODE SELECTION 🚨

Choose your trading mode:
1. TESTNET (Safe - No real money)
2. LIVE MONEY (Real funds - HIGH RISK)

Enter 1 or 2:`);

                    if (userChoice === null) return; // User cancelled

                    let testnet = true;
                    let confirmMessage = '';

                    if (userChoice === '1') {
                        testnet = true;
                        confirmMessage = '⚠️ You selected TESTNET mode. This is safe and uses no real money. Continue?';
                    } else if (userChoice === '2') {
                        testnet = false;
                        confirmMessage = `🚨 DANGER: REAL MONEY TRADING 🚨

You selected LIVE MONEY mode. This will:
- Use your REAL Binance account
- Trade with REAL money
- Risk REAL losses

Type "I UNDERSTAND THE RISKS" to confirm:`;

                        const riskConfirmation = prompt(confirmMessage);
                        if (riskConfirmation !== "I UNDERSTAND THE RISKS") {
                            alert('❌ Real money trading cancelled for safety');
                            return;
                        }
                    } else {
                        alert('❌ Invalid choice. Please select 1 or 2.');
                        return;
                    }

                    // Final confirmation
                    const finalConfirm = testnet ?
                        confirm('✅ Switch to TESTNET live mode?') :
                        confirm('🚨 FINAL WARNING: Switch to REAL MONEY trading?');

                    if (!finalConfirm) return;

                    // Make the API call with proper parameters
                    const response = await fetch('/api/toggle_live_mode', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({
                            live_mode: true,
                            testnet: testnet,
                            use_margin: false  // Default to spot trading
                        })
                    });

                    const result = await response.json();
                    if (result.status === 'success') {
                        state.isLiveMode = true;
                        updateButtons();
                        updateStatus();

                        const modeText = testnet ? 'TESTNET' : '🚨 REAL MONEY 🚨';
                        alert(`✅ Successfully switched to ${modeText} live trading mode!`);
                    } else {
                        alert(`❌ Failed to switch to live mode: ${result.message}`);
                    }
                } else {
                    // Switching to simulation mode
                    if (confirm('Switch to SIMULATION mode?')) {
                        const response = await fetch('/api/toggle_live_mode', {
                            method: 'POST',
                            headers: { 'Content-Type': 'application/json' },
                            body: JSON.stringify({ live_mode: false })
                        });

                        const result = await response.json();
                        if (result.status === 'success') {
                            state.isLiveMode = false;
                            updateButtons();
                            updateStatus();
                            alert('✅ Switched to SIMULATION mode');
                        } else {
                            alert(`❌ Failed to switch to simulation mode: ${result.message}`);
                        }
                    }
                }

                await updateDashboard();
            } catch (error) {
                console.error('Toggle mode error:', error);
                alert('Failed to toggle mode: ' + error.message);
            }
        }

        // Status popup functions
        function toggleStatusPopup() {
            const popup = document.getElementById('statusPopup');
            if (popup.style.display === 'none' || popup.style.display === '') {
                showStatusPopup();
            } else {
                hideStatusPopup();
            }
        }

        async function showStatusPopup() {
            const popup = document.getElementById('statusPopup');
            const content = document.getElementById('statusContent');

            try {
                // Get both trading status and AI monitoring data
                const [tradingData, aiData, riskData] = await Promise.all([
                    apiCall('/api/trading_status'),
                    apiCall('/api/ai_status').catch(() => null), // Don't fail if AI monitoring unavailable
                    apiCall('/api/risk_info').catch(() => null) // Get risk scaling info
                ]);

                let cleanModelType = tradingData.model_info.model_type
                    .replace(/TCN-CNN-PPO/g, 'AI Model')
                    .replace(/Ensemble/g, '')
                    .replace(/AI Model AI/g, 'AI Model')
                    .replace(/\s+/g, ' ')
                    .trim();

                if (cleanModelType === 'AI Model AI' || cleanModelType === '') {
                    cleanModelType = 'AI Model';
                }

                // Build AI monitoring section
                let aiSection = '';
                if (aiData && aiData.status === 'success') {
                    const ai = aiData.ai_monitoring;
                    const signals = aiData.signal_activity;
                    const trend = aiData.confidence_trend;

                    const confidencePercent = (ai.current_confidence * 100).toFixed(1);
                    const thresholdPercent = (ai.confidence_threshold * 100).toFixed(0);
                    const isAboveThreshold = ai.above_threshold;

                    aiSection = `
                        <h4 style="margin-top: 15px; color: #00d4aa;"><i class="fas fa-brain"></i> AI Confidence Monitor</h4>
                        <div class="status-item">
                            <span>AI Confidence:</span>
                            <span class="${isAboveThreshold ? 'status-value' : 'status-warning'}">${confidencePercent}%</span>
                        </div>
                        <div class="status-item">
                            <span>Threshold:</span>
                            <span class="status-value">${thresholdPercent}%</span>
                        </div>
                        <div class="status-item">
                            <span>Signal Status:</span>
                            <span class="${isAboveThreshold ? 'status-value' : 'status-warning'}">${isAboveThreshold ? 'READY' : 'WAITING'}</span>
                        </div>
                        <div class="status-item">
                            <span>Signals (1h):</span>
                            <span class="status-value">${signals.signals_last_hour}</span>
                        </div>
                        <div class="status-item">
                            <span>Trend:</span>
                            <span class="status-value">${trend.direction}</span>
                        </div>
                        <div class="status-item">
                            <span>Monitoring:</span>
                            <span class="${ai.active ? 'status-value' : 'status-error'}">${ai.active ? 'Active' : 'Inactive'}</span>
                        </div>
                    `;
                } else {
                    aiSection = `
                        <h4 style="margin-top: 15px; color: #ffa500;"><i class="fas fa-brain"></i> AI Confidence Monitor</h4>
                        <div class="status-item">
                            <span>Status:</span>
                            <span class="status-warning">Not Available</span>
                        </div>
                    `;
                }

                // Build compounding section
                let compoundingSection = '';
                if (riskData && riskData.status === 'success') {
                    const risk = riskData.risk_info;
                    const isCompounding = risk.compound_active;
                    const currentRisk = risk.current_risk;
                    const nextThreshold = risk.next_threshold;
                    const progress = risk.progress_to_next;

                    compoundingSection = `
                        <h4 style="margin-top: 15px; color: #00d4aa;"><i class="fas fa-chart-line"></i> Dynamic Risk Scaling</h4>
                        <div class="status-item">
                            <span>Current Risk:</span>
                            <span class="status-value">$${currentRisk.toFixed(2)}</span>
                        </div>
                        <div class="status-item">
                            <span>Risk Level:</span>
                            <span class="status-value">${risk.risk_level_percent.toFixed(1)}%</span>
                        </div>
                        <div class="status-item">
                            <span>Compounding:</span>
                            <span class="${isCompounding ? 'status-value' : 'status-warning'}">${isCompounding ? 'ACTIVE' : 'PENDING'}</span>
                        </div>
                        <div class="status-item">
                            <span>Next Threshold:</span>
                            <span class="status-value">$${nextThreshold.toFixed(0)}</span>
                        </div>
                        <div class="status-item">
                            <span>Progress:</span>
                            <span class="status-value">$${progress.toFixed(2)}</span>
                        </div>
                        <div class="status-item">
                            <span>Intervals:</span>
                            <span class="status-value">${risk.compound_intervals}</span>
                        </div>
                    `;
                } else {
                    compoundingSection = `
                        <h4 style="margin-top: 15px; color: #ffa500;"><i class="fas fa-chart-line"></i> Dynamic Risk Scaling</h4>
                        <div class="status-item">
                            <span>Status:</span>
                            <span class="status-warning">Not Available</span>
                        </div>
                    `;
                }

                content.innerHTML = `
                    <div class="status-item">
                        <span>System Name:</span>
                        <span class="status-value">Conservative Elite</span>
                    </div>
                    <div class="status-item">
                        <span>Model Type:</span>
                        <span class="status-value">TCN-CNN-PPO Ensemble</span>
                    </div>
                    <div class="status-item">
                        <span>Win Rate:</span>
                        <span class="status-value">93.2% (Out-of-Sample)</span>
                    </div>
                    <div class="status-item">
                        <span>Composite Score:</span>
                        <span class="status-value">79.1% (Conservative Elite)</span>
                    </div>
                    <div class="status-item">
                        <span>Model Status:</span>
                        <span style="color: #ffd700;">🔒 LOCKED</span>
                    </div>
                    <div class="status-item">
                        <span>Risk Scaling:</span>
                        <span class="status-value">Dynamic Compounding</span>
                    </div>
                    <div class="status-item">
                        <span>Trading Status:</span>
                        <span class="status-value">${tradingData.is_running ? 'Active' : 'Stopped'}</span>
                    </div>
                    <div class="status-item">
                        <span>Mode:</span>
                        <span class="status-value">${tradingData.is_live_mode ? 'LIVE' : 'Simulation'}</span>
                    </div>
                    <div class="status-item">
                        <span>BTC Price:</span>
                        <span class="status-value">$${tradingData.current_price.toLocaleString()}</span>
                    </div>
                    <div class="status-item">
                        <span>Total P&L:</span>
                        <span class="${tradingData.performance.total_profit >= 0 ? 'status-value' : 'status-error'}">$${tradingData.performance.total_profit.toFixed(2)}</span>
                    </div>
                    <div class="status-item">
                        <span>Open Positions:</span>
                        <span class="status-value">${tradingData.performance.open_positions}</span>
                    </div>
                    <div class="status-item">
                        <span>Total Trades:</span>
                        <span class="status-value">${tradingData.performance.total_trades}</span>
                    </div>
                `;

                // Check for test status
                fetch('/api/system_test_status')
                    .then(response => response.json())
                    .then(testData => {
                        if (testData.test_status && testData.test_status.test_completed && testData.test_status.test_passed) {
                            content.innerHTML += `
                                <div class="status-item" style="border-top: 1px solid rgba(0,212,170,0.3); padding-top: 10px; margin-top: 10px;">
                                    <span>Test Status:</span>
                                    <span style="color: #00d4aa;">✅ ${testData.test_status.message}</span>
                                </div>
                            `;
                        }
                    })
                    .catch(error => console.log('Test status not available'));

                content.innerHTML += `
                    ${aiSection}
                    ${compoundingSection}

                    <div style="margin-top: 20px; padding-top: 15px; border-top: 1px solid rgba(255,255,255,0.2);">
                        <h4 style="color: #00d4aa; margin-bottom: 10px;"><i class="fas fa-vial"></i> Test Functions</h4>
                        <button onclick="runTestTrade()" style="
                            background: linear-gradient(135deg, #00d4aa, #00a085);
                            color: white;
                            border: none;
                            padding: 8px 16px;
                            border-radius: 5px;
                            cursor: pointer;
                            margin-right: 10px;
                            margin-bottom: 5px;
                            font-size: 12px;
                            transition: all 0.3s ease;
                        " onmouseover="this.style.transform='scale(1.05)'" onmouseout="this.style.transform='scale(1)'">
                            🧪 Run Test Trade
                        </button>
                        <button onclick="clearRecentTrades()" style="
                            background: linear-gradient(135deg, #ff6b6b, #ee5a52);
                            color: white;
                            border: none;
                            padding: 8px 16px;
                            border-radius: 5px;
                            cursor: pointer;
                            margin-bottom: 5px;
                            font-size: 12px;
                            transition: all 0.3s ease;
                        " onmouseover="this.style.transform='scale(1.05)'" onmouseout="this.style.transform='scale(1)'">
                            🧹 Clear Recent Trades
                        </button>
                        <div id="testStatus" style="margin-top: 10px; font-size: 11px; color: #888;"></div>
                    </div>
                `;

                popup.style.display = 'block';
            } catch (error) {
                content.innerHTML = `<div class="status-error">Error loading status: ${error.message}</div>`;
                popup.style.display = 'block';
            }
        }

        function hideStatusPopup() {
            document.getElementById('statusPopup').style.display = 'none';
        }

        // Test Trade Functions
        async function runTestTrade() {
            const statusDiv = document.getElementById('testStatus');
            statusDiv.innerHTML = '<span style="color: #ffa500;">🧪 Running test trade...</span>';

            try {
                const response = await fetch('/api/run_test_trade', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });

                const result = await response.json();

                if (result.success) {
                    statusDiv.innerHTML = `
                        <span style="color: #00d4aa;">✅ Test Trade Passed!</span><br>
                        <small>P&L: $${result.pnl_usd.toFixed(2)} | Trades Cleared: ${result.trades_cleared ? 'Yes' : 'No'}</small>
                    `;

                    // Refresh dashboard to show cleared trades
                    setTimeout(() => {
                        updateDashboard();
                    }, 1000);
                } else {
                    statusDiv.innerHTML = `<span style="color: #ff6b6b;">❌ Test Failed: ${result.error}</span>`;
                }
            } catch (error) {
                statusDiv.innerHTML = `<span style="color: #ff6b6b;">❌ Error: ${error.message}</span>`;
            }
        }

        async function clearRecentTrades() {
            const statusDiv = document.getElementById('testStatus');
            statusDiv.innerHTML = '<span style="color: #ffa500;">🧹 Clearing recent trades...</span>';

            try {
                const response = await fetch('/api/clear_recent_trades', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });

                const result = await response.json();

                if (result.success) {
                    statusDiv.innerHTML = `
                        <span style="color: #00d4aa;">✅ Recent Trades Cleared!</span><br>
                        <small>DB: ${result.database_cleared ? 'Cleared' : 'Failed'} | CSV: ${result.csv_cleared ? 'Cleared' : 'Failed'}</small>
                    `;

                    // Refresh dashboard to show cleared trades
                    setTimeout(() => {
                        updateDashboard();
                    }, 1000);
                } else {
                    statusDiv.innerHTML = `<span style="color: #ff6b6b;">❌ Clear Failed: ${result.error}</span>`;
                }
            } catch (error) {
                statusDiv.innerHTML = `<span style="color: #ff6b6b;">❌ Error: ${error.message}</span>`;
            }
        }

        // Click outside to close popup
        document.addEventListener('click', function(event) {
            const popup = document.getElementById('statusPopup');
            const cog = document.querySelector('.status-cog');

            if (!popup.contains(event.target) && !cog.contains(event.target)) {
                hideStatusPopup();
            }
        });

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 Bitcoin Freedom Dashboard Initialized');
            updateDashboard();

            // Set up auto-refresh
            state.updateInterval = setInterval(updateDashboard, 5000);
        });

        // Cleanup on page unload
        window.addEventListener('beforeunload', function() {
            if (state.updateInterval) {
                clearInterval(state.updateInterval);
            }
        });
    </script>
</body>
</html>
