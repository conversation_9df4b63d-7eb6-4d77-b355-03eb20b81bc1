#!/usr/bin/env python3
"""
Search for 446% profit results from TCN-CNN-PPO testing
"""

import os
import re
import json
from pathlib import Path

def search_for_446_profit():
    """Search for the 446% profit results in all files"""
    
    print("🔍 SEARCHING FOR TCN-CNN-PPO 446% PROFIT RESULTS")
    print("=" * 60)
    
    search_patterns = [
        r"446.*%",
        r"446.*profit",
        r"profit.*446",
        r"4\.46",
        r"446\.",
        r"net_profit.*446",
        r"446.*net_profit"
    ]
    
    search_directories = [
        ".",
        "models",
        "logs", 
        "results",
        "training_results",
        "backtest_results"
    ]
    
    found_results = []
    
    for directory in search_directories:
        if os.path.exists(directory):
            print(f"\n📁 Searching in: {directory}")
            
            for root, dirs, files in os.walk(directory):
                for file in files:
                    if file.endswith(('.json', '.log', '.txt', '.md', '.py', '.csv')):
                        file_path = os.path.join(root, file)
                        
                        try:
                            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                                content = f.read()
                                
                                for pattern in search_patterns:
                                    matches = re.finditer(pattern, content, re.IGNORECASE)
                                    for match in matches:
                                        # Get context around the match
                                        start = max(0, match.start() - 100)
                                        end = min(len(content), match.end() + 100)
                                        context = content[start:end]
                                        
                                        found_results.append({
                                            'file': file_path,
                                            'pattern': pattern,
                                            'match': match.group(),
                                            'context': context.strip()
                                        })
                                        
                                        print(f"   ✅ Found in: {file_path}")
                                        print(f"      Pattern: {pattern}")
                                        print(f"      Match: {match.group()}")
                                        print(f"      Context: {context[:200]}...")
                                        print()
                        except Exception as e:
                            continue
    
    # Also search specifically in model performance files
    print(f"\n📊 SEARCHING MODEL PERFORMANCE FILES...")
    
    model_dirs = ["models/best_profit", "models/best_composite", "models"]
    for model_dir in model_dirs:
        if os.path.exists(model_dir):
            for file in os.listdir(model_dir):
                if file.endswith('.json'):
                    file_path = os.path.join(model_dir, file)
                    try:
                        with open(file_path, 'r') as f:
                            data = json.load(f)
                            
                        # Check for high profit values
                        if 'performance' in data:
                            perf = data['performance']
                            net_profit = perf.get('net_profit', 0)
                            
                            # Check if this could be the 446% result
                            if net_profit > 4000:  # Looking for high profits
                                profit_pct = (net_profit / 1000) * 100  # Assuming $1000 starting capital
                                
                                print(f"   📈 High Profit Model: {file}")
                                print(f"      Net Profit: ${net_profit:,.2f}")
                                print(f"      Estimated %: {profit_pct:.1f}%")
                                print(f"      Model ID: {data.get('model_id', 'N/A')}")
                                print(f"      Win Rate: {perf.get('win_rate', 0):.1%}")
                                print(f"      Trades/Day: {perf.get('trades_per_day', 0):.1f}")
                                print()
                                
                                if profit_pct > 400:  # Could be the 446% result
                                    found_results.append({
                                        'file': file_path,
                                        'type': 'high_profit_model',
                                        'net_profit': net_profit,
                                        'profit_percentage': profit_pct,
                                        'model_data': data
                                    })
                    except Exception as e:
                        continue
    
    # Search for any training reports or summaries
    print(f"\n📋 SEARCHING FOR TRAINING REPORTS...")
    
    report_patterns = [
        "*summary*",
        "*report*", 
        "*results*",
        "*training*",
        "*performance*"
    ]
    
    for pattern in report_patterns:
        for file_path in Path(".").rglob(pattern):
            if file_path.is_file() and file_path.suffix in ['.md', '.txt', '.log', '.json']:
                try:
                    content = file_path.read_text(encoding='utf-8', errors='ignore')
                    if '446' in content or 'profit' in content.lower():
                        print(f"   📄 Checking: {file_path}")
                        
                        # Look for profit-related content
                        lines = content.split('\n')
                        for i, line in enumerate(lines):
                            if '446' in line or ('profit' in line.lower() and any(char.isdigit() for char in line)):
                                print(f"      Line {i+1}: {line.strip()}")
                except Exception as e:
                    continue
    
    print(f"\n🎯 SEARCH SUMMARY:")
    print(f"=" * 60)
    
    if found_results:
        print(f"✅ Found {len(found_results)} potential matches!")
        
        # Show the most promising results
        high_profit_models = [r for r in found_results if r.get('type') == 'high_profit_model']
        if high_profit_models:
            print(f"\n🏆 HIGH PROFIT MODELS (Potential 446% candidates):")
            for result in high_profit_models:
                print(f"   📈 {result['file']}")
                print(f"      Net Profit: ${result['net_profit']:,.2f}")
                print(f"      Profit %: {result['profit_percentage']:.1f}%")
                print(f"      Model: {result['model_data'].get('model_id', 'N/A')}")
                print()
    else:
        print(f"⚠️ No direct matches found for '446%' profit results")
        print(f"💡 The 446% profit data may have been:")
        print(f"   • Moved to a different location")
        print(f"   • Archived or deleted")
        print(f"   • Stored in a different format")
        print(f"   • Part of a temporary training session")
    
    # Show the highest profit models found
    print(f"\n📊 HIGHEST PROFIT MODELS FOUND:")
    print(f"=" * 60)
    
    # Get all model performance data
    all_models = []
    for model_dir in ["models/best_profit", "models/best_composite"]:
        if os.path.exists(model_dir):
            for file in os.listdir(model_dir):
                if file.endswith('.json'):
                    try:
                        with open(os.path.join(model_dir, file), 'r') as f:
                            data = json.load(f)
                        if 'performance' in data:
                            all_models.append({
                                'file': file,
                                'net_profit': data['performance'].get('net_profit', 0),
                                'model_id': data.get('model_id', 'N/A'),
                                'win_rate': data['performance'].get('win_rate', 0),
                                'data': data
                            })
                    except:
                        continue
    
    # Sort by net profit
    all_models.sort(key=lambda x: x['net_profit'], reverse=True)
    
    print(f"🏆 TOP 5 HIGHEST PROFIT TCN-CNN-PPO MODELS:")
    for i, model in enumerate(all_models[:5], 1):
        profit_pct = (model['net_profit'] / 1000) * 100  # Assuming $1000 starting capital
        print(f"   {i}. Net Profit: ${model['net_profit']:,.2f} ({profit_pct:.1f}%)")
        print(f"      Model: {model['model_id']}")
        print(f"      Win Rate: {model['win_rate']:.1%}")
        print(f"      File: {model['file']}")
        print()
    
    return found_results

if __name__ == "__main__":
    try:
        results = search_for_446_profit()
        print(f"\n🎉 SEARCH COMPLETED!")
        if results:
            print(f"💡 Check the results above for your 446% profit data!")
        else:
            print(f"💡 The 446% profit results may need to be recreated through retraining.")
    except Exception as e:
        print(f"\n❌ Search failed: {e}")
        import traceback
        traceback.print_exc()
