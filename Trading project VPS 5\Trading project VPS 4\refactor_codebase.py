#!/usr/bin/env python3
"""
Complete Codebase Refactoring Script
===================================
Keeps only essential files for production_live_trading_app.py
Moves everything else to Archive folder
"""

import os
import shutil
import json
from datetime import datetime
from pathlib import Path

# Essential files for production live trading app
ESSENTIAL_FILES = {
    # Core application files
    'production_live_trading_app.py',
    'live_trading_web_app.py',
    
    # Core trading components
    'simple_binance_connector.py',
    'trade_csv_logger.py',
    'trade_database.py',
    'robust_metrics_calculator.py',
    'data_cache_manager.py',
    'ai_signal_monitor.py',
    'enhanced_retraining_system_tcn_cnn_ppo.py',
    'simplified_tcn_cnn_ppo_trainer.py',
    
    # Configuration and requirements
    'requirements.txt',
    
    # Data files (keep existing data)
    'bitcoin_freedom_trades.db',
    'bitcoin_freedom_trades.db-shm',
    'bitcoin_freedom_trades.db-wal',
    'trade_history.csv',
    
    # Essential directories
    'templates/',
    'models/',
    'logs/',
    'cache/',
    'config/',
    '__pycache__/',
}

# Essential template files
ESSENTIAL_TEMPLATES = {
    'bitcoin_freedom_dashboard.html',
    'production_live_trading_dashboard.html',
}

# Essential model files (keep all .pth files and metadata)
ESSENTIAL_MODEL_PATTERNS = {
    '*.pth',
    '*.json',
    '*.pkl',
    '*.joblib',
}

# Essential config files
ESSENTIAL_CONFIG_FILES = {
    'trading_config.py',
}

def create_archive_structure():
    """Create archive directory structure"""
    archive_base = Path("C:/Users/<USER>/Documents/Live Trading Systems Production/Trading project VPS 5/Archive")
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    archive_dir = archive_base / f"refactor_archive_{timestamp}"
    
    # Create archive directories
    archive_dir.mkdir(parents=True, exist_ok=True)
    (archive_dir / "webapp_alternatives").mkdir(exist_ok=True)
    (archive_dir / "documentation").mkdir(exist_ok=True)
    (archive_dir / "test_files").mkdir(exist_ok=True)
    (archive_dir / "backup_files").mkdir(exist_ok=True)
    (archive_dir / "training_scripts").mkdir(exist_ok=True)
    (archive_dir / "analysis_tools").mkdir(exist_ok=True)
    (archive_dir / "launchers").mkdir(exist_ok=True)
    
    return archive_dir

def categorize_file(filename):
    """Categorize files for appropriate archive folder"""
    filename_lower = filename.lower()
    
    if any(pattern in filename_lower for pattern in ['webapp', 'web_app', 'dashboard']):
        if filename not in ESSENTIAL_FILES:
            return "webapp_alternatives"
    
    if filename.endswith('.md') or filename.endswith('.txt') and 'requirements' not in filename:
        return "documentation"
    
    if filename.startswith('test_') or 'test' in filename_lower:
        return "test_files"
    
    if 'backup' in filename_lower or filename.endswith('.backup'):
        return "backup_files"
    
    if any(pattern in filename_lower for pattern in ['train', 'retrain', 'ml_training']):
        return "training_scripts"
    
    if any(pattern in filename_lower for pattern in ['analyze', 'check_', 'audit', 'verify']):
        return "analysis_tools"
    
    if any(pattern in filename_lower for pattern in ['launch', 'deploy', 'start_']):
        if filename != 'production_live_trading_app.py':
            return "launchers"
    
    return "webapp_alternatives"  # Default category

def is_essential_file(filepath):
    """Check if file is essential for production app"""
    filename = os.path.basename(filepath)
    
    # Check direct essential files
    if filename in ESSENTIAL_FILES:
        return True
    
    # Check essential directories
    for essential_dir in ESSENTIAL_FILES:
        if essential_dir.endswith('/') and essential_dir.rstrip('/') in str(filepath):
            return True
    
    return False

def move_file_to_archive(source_path, archive_dir, category):
    """Move file to appropriate archive category"""
    try:
        target_dir = archive_dir / category
        target_path = target_dir / os.path.basename(source_path)
        
        # Handle name conflicts
        counter = 1
        while target_path.exists():
            name, ext = os.path.splitext(os.path.basename(source_path))
            target_path = target_dir / f"{name}_{counter}{ext}"
            counter += 1
        
        shutil.move(str(source_path), str(target_path))
        return True
    except Exception as e:
        print(f"❌ Error moving {source_path}: {e}")
        return False

def refactor_codebase():
    """Main refactoring function"""
    print("🚀 STARTING COMPLETE CODEBASE REFACTORING")
    print("=" * 60)
    
    # Create archive structure
    archive_dir = create_archive_structure()
    print(f"📁 Archive directory created: {archive_dir}")
    
    # Get current directory
    current_dir = Path("Trading project VPS 5/Trading project VPS 4")
    
    if not current_dir.exists():
        print(f"❌ Source directory not found: {current_dir}")
        return False
    
    # Statistics
    stats = {
        'total_files': 0,
        'essential_kept': 0,
        'archived_files': 0,
        'errors': 0,
        'categories': {}
    }
    
    print(f"\n📊 ANALYZING FILES IN: {current_dir}")
    print("-" * 40)
    
    # Process all files
    for item in current_dir.iterdir():
        if item.is_file():
            stats['total_files'] += 1
            
            if is_essential_file(item):
                stats['essential_kept'] += 1
                print(f"✅ KEEPING: {item.name}")
            else:
                category = categorize_file(item.name)
                if move_file_to_archive(item, archive_dir, category):
                    stats['archived_files'] += 1
                    stats['categories'][category] = stats['categories'].get(category, 0) + 1
                    print(f"📦 ARCHIVED ({category}): {item.name}")
                else:
                    stats['errors'] += 1
    
    # Handle directories
    for item in current_dir.iterdir():
        if item.is_dir() and item.name not in ['templates', 'models', 'logs', 'cache', 'config', '__pycache__']:
            category = categorize_file(item.name)
            try:
                target_dir = archive_dir / category / item.name
                shutil.move(str(item), str(target_dir))
                stats['archived_files'] += 1
                stats['categories'][category] = stats['categories'].get(category, 0) + 1
                print(f"📦 ARCHIVED DIR ({category}): {item.name}")
            except Exception as e:
                print(f"❌ Error moving directory {item.name}: {e}")
                stats['errors'] += 1
    
    # Save refactoring report
    report = {
        'timestamp': datetime.now().isoformat(),
        'archive_location': str(archive_dir),
        'statistics': stats,
        'essential_files_kept': list(ESSENTIAL_FILES),
        'refactoring_successful': stats['errors'] == 0
    }
    
    report_path = current_dir / 'refactoring_report.json'
    with open(report_path, 'w') as f:
        json.dump(report, f, indent=2)
    
    # Print summary
    print("\n" + "=" * 60)
    print("📊 REFACTORING COMPLETE - SUMMARY")
    print("=" * 60)
    print(f"📁 Archive Location: {archive_dir}")
    print(f"📊 Total Files Processed: {stats['total_files']}")
    print(f"✅ Essential Files Kept: {stats['essential_kept']}")
    print(f"📦 Files Archived: {stats['archived_files']}")
    print(f"❌ Errors: {stats['errors']}")
    
    print(f"\n📂 Archive Categories:")
    for category, count in stats['categories'].items():
        print(f"   • {category}: {count} items")
    
    print(f"\n📄 Detailed report saved: {report_path}")
    
    if stats['errors'] == 0:
        print("\n🎉 REFACTORING SUCCESSFUL!")
        print("✅ Production live trading app is now streamlined")
        print("✅ All non-essential files archived safely")
    else:
        print(f"\n⚠️ REFACTORING COMPLETED WITH {stats['errors']} ERRORS")
        print("🔍 Check the errors above and manual intervention may be needed")
    
    return stats['errors'] == 0

if __name__ == "__main__":
    success = refactor_codebase()
    exit(0 if success else 1)
