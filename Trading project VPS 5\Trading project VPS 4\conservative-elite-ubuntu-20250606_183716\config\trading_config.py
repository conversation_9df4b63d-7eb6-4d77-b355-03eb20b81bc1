"""
Trading System Configuration
"""
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

class TradingConfig:
    # Grid Trading System Configuration (According to Specification)

    # Capital & Risk Management
    INITIAL_CAPITAL = 300.0  # $300 starting capital
    MARGIN_TYPE = 'CROSS'    # Cross margin trading
    FIXED_RISK_AMOUNT = 10.0  # $10 risk per trade
    REWARD_RATIO = 2.5       # 2.5:1 reward ratio (Enhanced)
    TARGET_PROFIT = FIXED_RISK_AMOUNT * REWARD_RATIO  # $25 profit per trade
    MAX_CONCURRENT_TRADES = 15  # Maximum 15 trades (50% of capital at risk)

    # 5% Per Trade Option (for accounts over $300)
    PERCENTAGE_RISK_MODE = False  # Enable percentage-based risk
    RISK_PERCENTAGE = 0.05       # 5% of account balance per trade
    MIN_ACCOUNT_FOR_PERCENTAGE = 300.0  # Minimum account size for percentage mode
    PERCENTAGE_REWARD_RATIO = 2.5  # 2.5:1 reward ratio for percentage mode

    # Grid Trading Parameters
    GRID_SPACING = 0.0025    # 0.25% FIXED grid spacing
    GRID_LEVELS_ABOVE = 10   # Grid levels above current price
    GRID_LEVELS_BELOW = 10   # Grid levels below current price

    # Model Training & Testing
    TRAINING_DAYS = 60       # 60 days training data
    TESTING_DAYS = 30        # 30 days out-of-sample testing
    TOTAL_CYCLE_DAYS = TRAINING_DAYS + TESTING_DAYS
    DATA_FREQUENCY = '1m'    # 1-minute candles for grid precision

    # Model Selection Criteria
    COMPOSITE_SCORE_THRESHOLD = 0.85  # 85% ideal threshold
    FALLBACK_DEPLOYMENT = True       # Deploy best available if <85%

    # Trading Parameters (BTC ONLY as per specification)
    TRADING_PAIRS = ['BTCUSDT']      # BTC only trading
    ETH_PAIR = 'ETHUSDT'             # For ETH/BTC ratio calculation
    COMMISSION_RATE = 0.001          # 0.1% commission on trade value
    MIN_TRADE_SIZE = 10.0            # $10 minimum trade size

    # Additional parameters for compatibility
    MAX_DRAWDOWN = 0.15  # 15%
    MAX_DAILY_TRADES = 50
    LOOKBACK_PERIOD = 24  # hours
    TRAINING_PERIOD = 60  # days
    TESTING_PERIOD = 30   # days
    COMPOSITE_THRESHOLD = 0.85  # 85%
    API_TIMEOUT = 30
    RATE_LIMIT_BUFFER = 0.1

    # Binance API (from environment variables)
    BINANCE_API_KEY = os.getenv('BINANCE_API_KEY', '')
    BINANCE_SECRET_KEY = os.getenv('BINANCE_SECRET_KEY', '')
    BINANCE_TESTNET = os.getenv('BINANCE_TESTNET', 'True').lower() == 'true'

    # Web Interface
    WEB_HOST = '127.0.0.1'
    WEB_PORT = 8080
    DEBUG_MODE = True

    # Directories
    BASE_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    MODELS_DIR = os.path.join(BASE_DIR, 'models')
    REPORTS_DIR = os.path.join(BASE_DIR, 'reports')
    DATA_DIR = os.path.join(BASE_DIR, 'data')
    LOGS_DIR = os.path.join(BASE_DIR, 'logs')

    # Logging
    LOG_LEVEL = 'INFO'
    LOG_FORMAT = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    LOG_ENCODING = 'utf-8'  # Fix Unicode issues on Windows
