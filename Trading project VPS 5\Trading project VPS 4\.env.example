# Conservative Elite Trading System Environment Variables
# Copy this file to .env and fill in your actual values

# Binance API Configuration (REQUIRED)
BINANCE_API_KEY=your_binance_api_key_here
BINANCE_SECRET_KEY=your_binance_secret_key_here
BINANCE_TESTNET=false

# Trading Configuration
TRADING_MODE=live
INITIAL_CAPITAL=300
RISK_PER_TRADE=10
TARGET_PROFIT=25
GRID_SPACING=0.0025
MAX_CONCURRENT_TRADES=15

# Conservative Elite Model Settings
MODEL_LOCKED=true
MODEL_ID=tcn_cnn_ppo_conservative_v3_20250604_111817
WIN_RATE=0.932
COMPOSITE_SCORE=0.791

# Security Settings
FLASK_SECRET_KEY=your_secret_key_here
CORS_ORIGINS=*

# Logging
LOG_LEVEL=INFO
LOG_TO_FILE=true

# Database
DATABASE_URL=sqlite:///bitcoin_freedom_trades.db

# Performance Settings
PRICE_UPDATE_INTERVAL=2
AI_PREDICTION_INTERVAL=5
TRADE_CHECK_INTERVAL=3

# Risk Management
MAX_DRAWDOWN=0.15
STOP_LOSS_PERCENTAGE=0.02
TAKE_PROFIT_PERCENTAGE=0.05

# Monitoring
ENABLE_METRICS=true
ENABLE_ALERTS=true
WEBHOOK_URL=
