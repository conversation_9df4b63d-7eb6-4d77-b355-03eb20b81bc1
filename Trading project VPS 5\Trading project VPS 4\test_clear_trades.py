#!/usr/bin/env python3
"""
Test Clear Trades Functionality
===============================
"""

import requests
import json

def test_clear_trades():
    base_url = "http://localhost:5000"
    
    print("🧪 TESTING CLEAR TRADES FUNCTIONALITY")
    print("=" * 40)
    
    # Test 1: Check current trades
    print("\n1️⃣ Current trades status...")
    try:
        response = requests.get(f"{base_url}/api/recent_trades", timeout=5)
        if response.status_code == 200:
            data = response.json()
            trades_count = len(data.get('trades', []))
            print(f"✅ Current trades count: {trades_count}")
        else:
            print(f"❌ Trades check failed: {response.status_code}")
    except Exception as e:
        print(f"❌ Trades check error: {e}")
    
    # Test 2: Test clear trades endpoint
    print("\n2️⃣ Testing clear trades endpoint...")
    try:
        response = requests.post(f"{base_url}/api/clear_recent_trades", timeout=10)
        print(f"Status Code: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Clear trades response:")
            print(f"   Success: {data.get('success', False)}")
            print(f"   Database cleared: {data.get('database_cleared', False)}")
            print(f"   CSV cleared: {data.get('csv_cleared', False)}")
            print(f"   Message: {data.get('message', 'No message')}")
        else:
            print(f"❌ Clear trades failed: {response.text}")
    except Exception as e:
        print(f"❌ Clear trades error: {e}")
    
    # Test 3: Check trades after clearing
    print("\n3️⃣ Checking trades after clearing...")
    try:
        response = requests.get(f"{base_url}/api/recent_trades", timeout=5)
        if response.status_code == 200:
            data = response.json()
            trades_count = len(data.get('trades', []))
            print(f"✅ Trades count after clearing: {trades_count}")
        else:
            print(f"❌ Post-clear trades check failed: {response.status_code}")
    except Exception as e:
        print(f"❌ Post-clear trades check error: {e}")
    
    print("\n" + "=" * 40)
    print("🎯 CLEAR TRADES TEST COMPLETE!")
    print("=" * 40)

if __name__ == "__main__":
    test_clear_trades()
