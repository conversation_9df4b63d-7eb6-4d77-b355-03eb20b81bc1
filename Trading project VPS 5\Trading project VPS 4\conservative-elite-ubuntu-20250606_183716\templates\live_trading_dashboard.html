<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🚀 TCN-CNN-PPO Live Trading Dashboard</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #0f1419 0%, #1a2332 50%, #2d3748 100%);
            color: #e2e8f0;
            min-height: 100vh;
            overflow-x: hidden;
        }

        .header {
            background: linear-gradient(135deg, rgba(0,0,0,0.4), rgba(0,0,0,0.2));
            padding: 25px;
            text-align: center;
            border-bottom: 3px solid #00d4aa;
            backdrop-filter: blur(10px);
            box-shadow: 0 4px 20px rgba(0,0,0,0.3);
        }

        .header h1 {
            font-size: 3em;
            margin-bottom: 15px;
            background: linear-gradient(45deg, #00d4aa, #4ade80, #22d3ee);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-shadow: 0 0 30px rgba(0,212,170,0.3);
            font-weight: 700;
        }

        .header .subtitle {
            font-size: 1.3em;
            color: #94a3b8;
            margin-bottom: 20px;
            font-weight: 300;
        }

        .model-info {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 20px;
            max-width: 1200px;
            margin-left: auto;
            margin-right: auto;
        }
        
        .model-stat {
            background: rgba(255,255,255,0.1);
            padding: 10px 20px;
            border-radius: 10px;
            text-align: center;
        }
        
        .model-stat .label {
            font-size: 0.9em;
            opacity: 0.8;
        }
        
        .model-stat .value {
            font-size: 1.3em;
            font-weight: bold;
            margin-top: 5px;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .controls {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin-bottom: 30px;
            flex-wrap: wrap;
        }
        
        .btn {
            padding: 12px 25px;
            border: none;
            border-radius: 8px;
            font-size: 1.1em;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            text-transform: uppercase;
        }
        
        .btn-start {
            background: #4CAF50;
            color: white;
        }
        
        .btn-stop {
            background: #f44336;
            color: white;
        }
        
        .btn-toggle {
            background: #FF9800;
            color: white;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.3);
        }
        
        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }
        
        .dashboard {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .card {
            background: rgba(255,255,255,0.1);
            border-radius: 15px;
            padding: 20px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
        }
        
        .card h3 {
            margin-bottom: 15px;
            color: #4CAF50;
            font-size: 1.3em;
        }
        
        .status-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
        }
        
        .status-item {
            text-align: center;
        }
        
        .status-item .label {
            font-size: 0.9em;
            opacity: 0.8;
            margin-bottom: 5px;
        }
        
        .status-item .value {
            font-size: 1.5em;
            font-weight: bold;
        }
        
        .profit {
            color: #4CAF50;
        }
        
        .loss {
            color: #f44336;
        }
        
        .running {
            color: #4CAF50;
        }
        
        .stopped {
            color: #f44336;
        }
        
        .live-mode {
            color: #FF9800;
        }
        
        .sim-mode {
            color: #2196F3;
        }
        
        .trades-section {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        
        .trades-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
        }
        
        .trades-table th,
        .trades-table td {
            padding: 8px 12px;
            text-align: left;
            border-bottom: 1px solid rgba(255,255,255,0.2);
        }
        
        .trades-table th {
            background: rgba(255,255,255,0.1);
            font-weight: bold;
        }
        
        .trades-table tr:hover {
            background: rgba(255,255,255,0.05);
        }
        
        .trade-buy {
            color: #4CAF50;
        }
        
        .trade-sell {
            color: #f44336;
        }
        
        .footer {
            text-align: center;
            padding: 20px;
            margin-top: 30px;
            border-top: 1px solid rgba(255,255,255,0.2);
            opacity: 0.8;
        }
        
        .mode-indicator {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 10px 20px;
            border-radius: 20px;
            font-weight: bold;
            font-size: 0.9em;
        }
        
        .price-display {
            font-size: 2em;
            font-weight: bold;
            text-align: center;
            margin: 20px 0;
            color: #FFD700;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
        }
        
        @media (max-width: 768px) {
            .dashboard {
                grid-template-columns: 1fr;
            }
            
            .trades-section {
                grid-template-columns: 1fr;
            }
            
            .model-info {
                flex-direction: column;
                align-items: center;
            }
            
            .controls {
                flex-direction: column;
                align-items: center;
            }
        }
    </style>
</head>
<body>
    <div class="mode-indicator" id="modeIndicator">
        SIMULATION MODE
    </div>
    
    <div class="header">
        <h1>🚀 TCN-CNN-PPO Live Trading</h1>
        <div class="model-info">
            <div class="model-stat">
                <div class="label">Model</div>
                <div class="value" id="modelId">Loading...</div>
            </div>
            <div class="model-stat">
                <div class="label">Composite Score</div>
                <div class="value" id="compositeScore">--%</div>
            </div>
            <div class="model-stat">
                <div class="label">Target Win Rate</div>
                <div class="value" id="targetWinRate">--%</div>
            </div>
            <div class="model-stat">
                <div class="label">Account Size</div>
                <div class="value" id="accountSize">$--</div>
            </div>
        </div>
    </div>
    
    <div class="container">
        <div class="controls">
            <button class="btn btn-start" id="startBtn" onclick="startTrading()">
                🚀 Start Trading
            </button>
            <button class="btn btn-stop" id="stopBtn" onclick="stopTrading()" disabled>
                🛑 Stop Trading
            </button>
            <button class="btn btn-toggle" id="toggleBtn" onclick="toggleMode()">
                🔄 Switch to Live Mode
            </button>
        </div>

        <!-- Test Trading Controls -->
        <div class="controls" style="margin-top: 20px; border-top: 2px solid rgba(255,255,255,0.2); padding-top: 20px;">
            <h3 style="text-align: center; margin-bottom: 15px; color: #FFD700;">🧪 Test Trading Functions</h3>
            <button class="btn" style="background: #9C27B0; color: white;" onclick="forceTestTrade('BUY')">
                🧪 Force BUY Test
            </button>
            <button class="btn" style="background: #9C27B0; color: white;" onclick="forceTestTrade('SELL')">
                🧪 Force SELL Test
            </button>
            <button class="btn" style="background: #673AB7; color: white;" onclick="runTestCycle()">
                ⚡ Run Test Cycle
            </button>
            <button class="btn" style="background: #FF5722; color: white;" onclick="closeAllPositions()">
                🔄 Close All Positions
            </button>
            <button class="btn" style="background: #607D8B; color: white;" onclick="resetStats()">
                🔄 Reset Stats
            </button>
            <button class="btn" style="background: #4CAF50; color: white;" onclick="checkCrossMargin()">
                📊 Check Cross Margin
            </button>
        </div>
        
        <div class="price-display" id="currentPrice">
            BTC: $--,---
        </div>
        
        <div class="dashboard">
            <div class="card">
                <h3>📊 Trading Status</h3>
                <div class="status-grid">
                    <div class="status-item">
                        <div class="label">Status</div>
                        <div class="value" id="tradingStatus">Stopped</div>
                    </div>
                    <div class="status-item">
                        <div class="label">Mode</div>
                        <div class="value" id="tradingMode">Simulation</div>
                    </div>
                    <div class="status-item">
                        <div class="label">Open Positions</div>
                        <div class="value" id="openPositions">0</div>
                    </div>
                    <div class="status-item">
                        <div class="label">Daily Trades</div>
                        <div class="value" id="dailyTrades">0</div>
                    </div>
                </div>
            </div>
            
            <div class="card">
                <h3>💰 Performance</h3>
                <div class="status-grid">
                    <div class="status-item">
                        <div class="label">Equity</div>
                        <div class="value" id="equity">$300.00</div>
                    </div>
                    <div class="status-item">
                        <div class="label">Total P&L</div>
                        <div class="value" id="totalPnl">$0.00</div>
                    </div>
                    <div class="status-item">
                        <div class="label">Win Rate</div>
                        <div class="value" id="winRate">--%</div>
                    </div>
                    <div class="status-item">
                        <div class="label">Daily P&L</div>
                        <div class="value" id="dailyPnl">$0.00</div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="trades-section">
            <div class="card">
                <h3>🔄 Open Positions</h3>
                <table class="trades-table" id="openPositionsTable">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Direction</th>
                            <th>Entry</th>
                            <th>Current P&L</th>
                            <th>Duration</th>
                        </tr>
                    </thead>
                    <tbody id="openPositionsBody">
                        <tr>
                            <td colspan="5" style="text-align: center; opacity: 0.6;">No open positions</td>
                        </tr>
                    </tbody>
                </table>
            </div>
            
            <div class="card">
                <h3>📈 Recent Trades</h3>
                <table class="trades-table" id="recentTradesTable">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Direction</th>
                            <th>Entry</th>
                            <th>P&L</th>
                            <th>Status</th>
                        </tr>
                    </thead>
                    <tbody id="recentTradesBody">
                        <tr>
                            <td colspan="5" style="text-align: center; opacity: 0.6;">No trades yet</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    
    <div class="footer">
        <p id="footerModelInfo">🏆 Loading Model Information...</p>
        <p id="healthStatus">🔍 System Health Check: <span id="healthIndicator">Checking...</span></p>
        <p>⚠️ Test thoroughly in simulation mode before enabling live trading</p>
    </div>

    <script>
        let isRunning = false;
        let isLiveMode = false;
        
        // Update dashboard every 2 seconds
        setInterval(updateDashboard, 2000);
        
        // Initial load
        updateDashboard();
        
        function startTrading() {
            fetch('/api/start_trading', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success') {
                    isRunning = true;
                    updateButtons();
                    console.log('Trading started');
                } else {
                    alert('Error: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Failed to start trading');
            });
        }
        
        function stopTrading() {
            fetch('/api/stop_trading', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success') {
                    isRunning = false;
                    updateButtons();
                    console.log('Trading stopped');
                } else {
                    alert('Error: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Failed to stop trading');
            });
        }
        
        function toggleMode() {
            const newMode = !isLiveMode;
            
            if (newMode && !confirm('⚠️ WARNING: You are about to switch to LIVE MODE. This will use real money on Binance. Are you sure?')) {
                return;
            }
            
            fetch('/api/toggle_live_mode', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    live_mode: newMode
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success') {
                    isLiveMode = data.live_mode;
                    updateButtons();
                    updateModeIndicator();
                    console.log('Mode switched to:', isLiveMode ? 'LIVE' : 'SIMULATION');
                } else {
                    alert('Error: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Failed to toggle mode');
            });
        }
        
        function updateButtons() {
            const startBtn = document.getElementById('startBtn');
            const stopBtn = document.getElementById('stopBtn');
            const toggleBtn = document.getElementById('toggleBtn');
            
            startBtn.disabled = isRunning;
            stopBtn.disabled = !isRunning;
            
            toggleBtn.textContent = isLiveMode ? '🔄 Switch to Simulation' : '🔄 Switch to Live Mode';
            toggleBtn.disabled = isRunning; // Can't change mode while trading
        }
        
        function updateModeIndicator() {
            const indicator = document.getElementById('modeIndicator');
            if (isLiveMode) {
                indicator.textContent = '🔴 LIVE MODE';
                indicator.className = 'mode-indicator live-mode';
                indicator.style.background = '#f44336';
            } else {
                indicator.textContent = '🟢 SIMULATION MODE';
                indicator.className = 'mode-indicator sim-mode';
                indicator.style.background = '#2196F3';
            }
        }
        
        function updateDashboard() {
            fetch('/api/trading_status')
                .then(response => response.json())
                .then(data => {
                    // Update model info
                    document.getElementById('modelId').textContent = data.model_info.model_id.split('_')[0].toUpperCase();
                    document.getElementById('compositeScore').textContent = data.model_info.composite_score + '%';
                    document.getElementById('targetWinRate').textContent = data.model_info.win_rate_target + '%';
                    document.getElementById('accountSize').textContent = '$' + data.performance.account_size;
                    
                    // Update current price
                    document.getElementById('currentPrice').textContent = 'BTC: $' + data.current_price.toLocaleString();
                    
                    // Update status
                    isRunning = data.is_running;
                    isLiveMode = data.is_live_mode;
                    
                    document.getElementById('tradingStatus').textContent = isRunning ? 'Running' : 'Stopped';
                    document.getElementById('tradingStatus').className = 'value ' + (isRunning ? 'running' : 'stopped');
                    
                    document.getElementById('tradingMode').textContent = isLiveMode ? 'Live' : 'Simulation';
                    document.getElementById('tradingMode').className = 'value ' + (isLiveMode ? 'live-mode' : 'sim-mode');
                    
                    // Update performance
                    document.getElementById('equity').textContent = '$' + data.performance.equity.toLocaleString();
                    document.getElementById('totalPnl').textContent = '$' + data.performance.total_profit.toFixed(2);
                    document.getElementById('totalPnl').className = 'value ' + (data.performance.total_profit >= 0 ? 'profit' : 'loss');
                    document.getElementById('winRate').textContent = data.performance.win_rate + '%';
                    document.getElementById('dailyPnl').textContent = '$' + data.performance.daily_pnl.toFixed(2);
                    document.getElementById('dailyPnl').className = 'value ' + (data.performance.daily_pnl >= 0 ? 'profit' : 'loss');
                    document.getElementById('openPositions').textContent = data.performance.open_positions;
                    document.getElementById('dailyTrades').textContent = data.performance.daily_trades;

                    // Update footer with real model information
                    const modelInfo = `🏆 ${data.model_info.model_type} (Cycle ${data.model_info.cycle}) | ${data.model_info.composite_score}% Composite Score | ${data.model_info.win_rate_target}% Win Rate | Ready for Binance Integration`;
                    document.getElementById('footerModelInfo').textContent = modelInfo;

                    updateButtons();
                    updateModeIndicator();
                })
                .catch(error => {
                    console.error('Error updating dashboard:', error);
                    // Update health status on error
                    document.getElementById('healthIndicator').textContent = 'Connection Error';
                    document.getElementById('healthIndicator').style.color = '#f44336';
                });
            
            // Update open positions
            updateOpenPositions();
            
            // Update recent trades
            updateRecentTrades();

            // Run background health check
            performHealthCheck();
        }

        // Background Health Check System
        function performHealthCheck() {
            fetch('/health')
                .then(response => response.json())
                .then(data => {
                    const healthIndicator = document.getElementById('healthIndicator');

                    if (data.status === 'healthy') {
                        // All systems operational
                        healthIndicator.textContent = '✅ All Systems Operational';
                        healthIndicator.style.color = '#4CAF50';

                        // Check individual components
                        const issues = [];
                        if (data.binance_connector === 'disconnected') issues.push('Binance Disconnected');
                        if (data.trading_engine === 'stopped') issues.push('Engine Stopped');
                        if (data.database !== 'available') issues.push('Database Issue');
                        if (data.csv_logger !== 'available') issues.push('CSV Logger Issue');

                        if (issues.length > 0) {
                            healthIndicator.textContent = `⚠️ ${issues.join(', ')}`;
                            healthIndicator.style.color = '#FF9800';
                        }
                    } else {
                        // System has issues
                        healthIndicator.textContent = '❌ System Issues Detected';
                        healthIndicator.style.color = '#f44336';
                    }
                })
                .catch(error => {
                    console.error('Health check failed:', error);
                    const healthIndicator = document.getElementById('healthIndicator');
                    healthIndicator.textContent = '❌ Health Check Failed';
                    healthIndicator.style.color = '#f44336';
                });
        }
        
        function updateOpenPositions() {
            fetch('/api/open_positions')
                .then(response => response.json())
                .then(positions => {
                    const tbody = document.getElementById('openPositionsBody');
                    
                    if (positions.length === 0) {
                        tbody.innerHTML = '<tr><td colspan="5" style="text-align: center; opacity: 0.6;">No open positions</td></tr>';
                        return;
                    }
                    
                    tbody.innerHTML = positions.map(pos => `
                        <tr>
                            <td>${pos.trade_id.split('_').pop()}</td>
                            <td class="trade-${pos.direction.toLowerCase()}">${pos.direction}</td>
                            <td>$${pos.entry_price.toLocaleString()}</td>
                            <td class="${pos.current_pnl >= 0 ? 'profit' : 'loss'}">$${pos.current_pnl.toFixed(2)}</td>
                            <td>${pos.duration}</td>
                        </tr>
                    `).join('');
                })
                .catch(error => {
                    console.error('Error updating open positions:', error);
                });
        }
        
        function updateRecentTrades() {
            fetch('/api/recent_trades')
                .then(response => response.json())
                .then(trades => {
                    console.log('🔍 DEBUG: Recent trades API response:', trades);
                    console.log('🔍 DEBUG: Number of trades:', trades.length);

                    const tbody = document.getElementById('recentTradesBody');

                    if (trades.length === 0) {
                        console.log('🔍 DEBUG: No trades found, showing empty message');
                        tbody.innerHTML = '<tr><td colspan="5" style="text-align: center; opacity: 0.6;">No trades yet</td></tr>';
                        return;
                    }

                    console.log('🔍 DEBUG: Processing trades for display');

                    tbody.innerHTML = trades.slice(-10).reverse().map(trade => `
                        <tr>
                            <td>${trade.trade_id.split('_').pop()}</td>
                            <td class="trade-${trade.direction.toLowerCase()}">${trade.direction}</td>
                            <td>$${trade.entry_price.toLocaleString()}</td>
                            <td class="${trade.pnl >= 0 ? 'profit' : 'loss'}">$${trade.pnl.toFixed(2)}</td>
                            <td>${trade.status.replace('CLOSED_', '')}</td>
                        </tr>
                    `).join('');
                })
                .catch(error => {
                    console.error('Error updating recent trades:', error);
                });
        }

        // Test Trading Functions
        function forceTestTrade(direction) {
            if (!isRunning) {
                alert('⚠️ Please start trading engine first!');
                return;
            }

            fetch('/api/force_test_trade', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    direction: direction
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success') {
                    alert(`✅ Test ${direction} trade created!\n\nTrade ID: ${data.trade_id}\nEntry Price: $${data.entry_price.toLocaleString()}\nConfidence: ${data.confidence}%\nRisk: $${data.risk_amount}\nTarget: $${data.target_profit}`);
                    console.log('Test trade created:', data);
                } else {
                    alert('❌ Error: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error forcing test trade:', error);
                alert('❌ Failed to create test trade');
            });
        }

        function runTestCycle() {
            if (!isRunning) {
                alert('⚠️ Please start trading engine first!');
                return;
            }

            fetch('/api/test_trading_cycle', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success') {
                    alert(`✅ Test trading cycle completed!\n\nTrade ID: ${data.trade_id}\nEntry: $${data.entry_price.toLocaleString()}\nExit: $${data.exit_price.toLocaleString()}\nP&L: $${data.pnl.toFixed(2)}\nDuration: ${data.duration}`);
                    console.log('Test cycle completed:', data);
                } else {
                    alert('❌ Error: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error running test cycle:', error);
                alert('❌ Failed to run test cycle');
            });
        }

        function closeAllPositions() {
            if (!isRunning) {
                alert('⚠️ Please start trading engine first!');
                return;
            }

            if (!confirm('⚠️ Close all open positions? This will force close all trades at current market price.')) {
                return;
            }

            fetch('/api/close_all_positions', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success') {
                    alert(`✅ Closed ${data.closed_count} positions successfully!`);
                    console.log('Positions closed:', data);
                } else {
                    alert('❌ Error: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error closing positions:', error);
                alert('❌ Failed to close positions');
            });
        }

        function resetStats() {
            if (!confirm('⚠️ Reset all trading statistics? This will clear all trade history and reset account to $300.')) {
                return;
            }

            fetch('/api/reset_trading_stats', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success') {
                    alert(`✅ Trading statistics reset successfully!\n\nAccount reset to: $${data.account_size}`);
                    console.log('Stats reset:', data);
                } else {
                    alert('❌ Error: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error resetting stats:', error);
                alert('❌ Failed to reset statistics');
            });
        }

        function checkCrossMargin() {
            fetch('/api/cross_margin_analysis')
                .then(response => response.json())
                .then(data => {
                    const buy = data.buy_position;
                    const sell = data.sell_position;
                    const config = data.account_config;
                    const accuracy = data.accuracy_check;

                    const analysisText = `📊 CROSS MARGIN ANALYSIS - BTC: $${data.current_btc_price.toLocaleString()}

🏦 ACCOUNT CONFIGURATION:
   • Account Size: $${config.account_size}
   • Margin Type: ${config.margin_type}
   • Risk per Trade: $${config.risk_per_trade}
   • Target Ratio: ${config.reward_ratio}:1
   • Grid Size: ${data.grid_size_percent}% ($${data.grid_size_usd})

📈 BUY POSITION ANALYSIS:
   • Position Size: ${buy.position_size_btc} BTC ($${buy.position_size_usd})
   • Stop Loss: $${buy.stop_loss_price.toLocaleString()} (-${buy.stop_distance_grids} grids)
   • Take Profit: $${buy.take_profit_price.toLocaleString()} (+${buy.target_distance_grids} grids)
   • Actual Risk: $${buy.actual_risk}
   • Expected Profit: $${buy.expected_profit}
   • Risk:Reward Ratio: 1:${accuracy.risk_reward_ratio_buy}

📉 SELL POSITION ANALYSIS:
   • Position Size: ${sell.position_size_btc} BTC ($${sell.position_size_usd})
   • Stop Loss: $${sell.stop_loss_price.toLocaleString()} (+${sell.stop_distance_grids} grids)
   • Take Profit: $${sell.take_profit_price.toLocaleString()} (-${sell.target_distance_grids} grids)
   • Actual Risk: $${sell.actual_risk}
   • Expected Profit: $${sell.expected_profit}
   • Risk:Reward Ratio: 1:${accuracy.risk_reward_ratio_sell}

✅ ACCURACY CHECK:
   • Target Ratio: 1:${accuracy.target_ratio}
   • BUY Achieved: 1:${accuracy.risk_reward_ratio_buy}
   • SELL Achieved: 1:${accuracy.risk_reward_ratio_sell}
   • Grid Alignment: ${accuracy.grid_accuracy}

${accuracy.risk_reward_ratio_buy >= 2.4 && accuracy.risk_reward_ratio_sell >= 2.4 ? '✅ RATIOS ACCURATE!' : '⚠️ Check ratio calculations'}`;

                    alert(analysisText);
                    console.log('Cross Margin Analysis:', data);
                })
                .catch(error => {
                    console.error('Error getting cross margin analysis:', error);
                    alert('❌ Failed to get cross margin analysis');
                });
        }
    </script>
</body>
</html>
