#!/usr/bin/env python3
"""
Quick WebApp Functionality Test
==============================
Tests all the issues mentioned by the user
"""

import requests
import json
import time

def test_webapp_functionality():
    base_url = "http://localhost:5000"
    
    print("🧪 QUICK WEBAPP FUNCTIONALITY TEST")
    print("=" * 50)
    
    # Test 1: Stop Trading Button
    print("\n1️⃣ Testing Stop Trading Button...")
    try:
        response = requests.post(f"{base_url}/api/stop_trading", timeout=5)
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Stop Trading Button: WORKING")
            print(f"   Response: {result.get('message', 'Success')}")
        else:
            print(f"❌ Stop Trading Button: FAILED ({response.status_code})")
    except Exception as e:
        print(f"❌ Stop Trading Button: ERROR - {e}")
    
    # Test 2: Cog Status (Status Popup)
    print("\n2️⃣ Testing Cog Status Popup...")
    try:
        # Test the endpoints that the cog status uses
        endpoints = [
            '/api/trading_status',
            '/api/ai_status', 
            '/api/risk_info'
        ]
        
        working_endpoints = 0
        for endpoint in endpoints:
            try:
                response = requests.get(f"{base_url}{endpoint}", timeout=5)
                if response.status_code == 200:
                    working_endpoints += 1
                    print(f"   ✅ {endpoint}: Working")
                else:
                    print(f"   ⚠️ {endpoint}: Status {response.status_code}")
            except:
                print(f"   ❌ {endpoint}: Failed")
        
        if working_endpoints >= 1:
            print(f"✅ Cog Status Popup: WORKING ({working_endpoints}/3 endpoints)")
        else:
            print(f"❌ Cog Status Popup: FAILED (no endpoints working)")
            
    except Exception as e:
        print(f"❌ Cog Status Popup: ERROR - {e}")
    
    # Test 3: Binance Live Section
    print("\n3️⃣ Testing Binance Live Section...")
    try:
        response = requests.get(f"{base_url}/api/binance_status", timeout=10)
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Binance Live Section: WORKING")
            print(f"   Connected: {data.get('connected', 'Unknown')}")
            print(f"   BTC Price: ${data.get('current_btc_price', 0):,.2f}")
            print(f"   Connection Test: {data.get('connection_test', 'Unknown')}")
        else:
            print(f"❌ Binance Live Section: FAILED ({response.status_code})")
    except Exception as e:
        print(f"❌ Binance Live Section: ERROR - {e}")
    
    # Test 4: Health Check
    print("\n4️⃣ Testing Health Check...")
    try:
        response = requests.get(f"{base_url}/health", timeout=5)
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Health Check: PASSED")
            print(f"   Status: {data.get('status', 'Unknown')}")
            print(f"   Trading Engine: {data.get('trading_engine', 'Unknown')}")
            print(f"   Current Price: ${data.get('current_price', 0):,.2f}")
        else:
            print(f"❌ Health Check: FAILED ({response.status_code})")
    except Exception as e:
        print(f"❌ Health Check: ERROR - {e}")
    
    # Test 5: Preflight Check
    print("\n5️⃣ Testing Preflight Check...")
    try:
        response = requests.get(f"{base_url}/api/preflight_check", timeout=10)
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Preflight Check: PASSED")
            print(f"   Trading Readiness: {data.get('trading_readiness', 'Unknown')}")
        else:
            print(f"❌ Preflight Check: FAILED ({response.status_code})")
    except Exception as e:
        print(f"❌ Preflight Check: ERROR - {e}")
    
    print("\n" + "=" * 50)
    print("🎯 WEBAPP TEST COMPLETE!")
    print("🌐 Dashboard URL: http://localhost:5000")
    print("=" * 50)

if __name__ == "__main__":
    test_webapp_functionality()
