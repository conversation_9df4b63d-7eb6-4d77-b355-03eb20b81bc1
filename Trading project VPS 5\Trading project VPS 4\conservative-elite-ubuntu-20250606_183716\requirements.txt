# Real Money Trading System Requirements
# Install with: pip install -r requirements.txt

# Core Dependencies
streamlit>=1.28.0
plotly>=5.17.0
pandas>=2.0.0
numpy>=1.24.0
ccxt>=4.1.0

# Machine Learning
torch>=2.0.0
scikit-learn>=1.3.0
tensorflow>=2.13.0
stable-baselines3>=1.6.0
gymnasium>=0.26.0
xgboost>=1.6.0

# Security & Encryption
cryptography>=41.0.0

# HTTP Requests
requests>=2.31.0
aiohttp>=3.8.0

# Data Processing
ta>=0.10.0
yfinance>=0.2.0
python-binance>=1.0.15

# Utilities
python-dotenv>=1.0.0
schedule>=1.1.0
joblib>=1.1.0

# Visualization
matplotlib>=3.7.0
seaborn>=0.12.0
dash>=2.15.0
dash-bootstrap-components>=1.5.0

# Development & Testing
pytest>=7.4.0
pytest-asyncio>=0.21.0
optuna>=3.4.0
tensorboard>=2.13.0

# Optional: Performance Monitoring
psutil>=5.9.0
memory-profiler>=0.61.0
