#!/usr/bin/env python3
"""
SIMPLE BINANCE CONNECTOR
========================
A lightweight Binance connector that doesn't require CCXT.
Uses direct REST API calls to Binance.
"""

import requests
import hmac
import hashlib
import time
import json
from urllib.parse import urlencode
from typing import Dict, Optional, Any

class SimpleBinanceConnector:
    """Simple Binance connector using direct REST API calls."""
    
    def __init__(self, api_key: str, secret_key: str, testnet: bool = False):
        self.api_key = api_key
        self.secret_key = secret_key
        self.testnet = testnet
        
        # API endpoints
        if testnet:
            self.base_url = "https://testnet.binance.vision"
        else:
            self.base_url = "https://api.binance.com"
        
        self.is_connected = False
        self.session = requests.Session()
        self.session.headers.update({
            'X-MBX-APIKEY': self.api_key,
            'Content-Type': 'application/json'
        })
        
        print(f"🔗 Simple Binance Connector initialized")
        print(f"   Testnet: {testnet}")
        print(f"   Base URL: {self.base_url}")
    
    def _generate_signature(self, params: Dict) -> str:
        """Generate HMAC SHA256 signature for Binance API."""
        query_string = urlencode(params)
        return hmac.new(
            self.secret_key.encode('utf-8'),
            query_string.encode('utf-8'),
            hashlib.sha256
        ).hexdigest()
    
    def _make_request(self, method: str, endpoint: str, params: Dict = None, signed: bool = False) -> Optional[Dict]:
        """Make a request to Binance API."""
        try:
            if params is None:
                params = {}
            
            if signed:
                params['timestamp'] = int(time.time() * 1000)
                params['signature'] = self._generate_signature(params)
            
            url = f"{self.base_url}{endpoint}"
            
            if method.upper() == 'GET':
                response = self.session.get(url, params=params)
            elif method.upper() == 'POST':
                response = self.session.post(url, params=params)
            else:
                raise ValueError(f"Unsupported method: {method}")
            
            if response.status_code == 200:
                return response.json()
            else:
                print(f"❌ Binance API Error: {response.status_code} - {response.text}")
                return None
                
        except Exception as e:
            print(f"❌ Request error: {e}")
            return None
    
    def test_connection(self) -> bool:
        """Test connection to Binance API."""
        try:
            # Test public endpoint first
            result = self._make_request('GET', '/api/v3/ping')
            if result is None:
                print("❌ Cannot reach Binance API")
                return False
            
            # Test private endpoint
            account_info = self._make_request('GET', '/api/v3/account', signed=True)
            if account_info is None:
                print("❌ Cannot authenticate with Binance API")
                return False
            
            self.is_connected = True
            print("✅ Binance connection successful")
            return True
            
        except Exception as e:
            print(f"❌ Connection test failed: {e}")
            self.is_connected = False
            return False
    
    def get_account_balance(self) -> Optional[Dict]:
        """Get account balance information."""
        try:
            if not self.is_connected:
                if not self.test_connection():
                    return None
            
            account_info = self._make_request('GET', '/api/v3/account', signed=True)
            if account_info:
                return {
                    'info': account_info,
                    'balances': account_info.get('balances', [])
                }
            return None
            
        except Exception as e:
            print(f"❌ Error getting account balance: {e}")
            return None
    
    def get_margin_account(self) -> Optional[Dict]:
        """Get margin account information."""
        try:
            if not self.is_connected:
                if not self.test_connection():
                    return None

            margin_info = self._make_request('GET', '/sapi/v1/margin/account', signed=True)
            return margin_info

        except Exception as e:
            print(f"❌ Error getting margin account: {e}")
            return None

    def get_portfolio_balance_for_rebalancing(self) -> Optional[Dict]:
        """Get portfolio balance in format compatible with IntelligentPortfolioRebalancer."""
        try:
            if not self.is_connected:
                if not self.test_connection():
                    return None

            # Try margin account first, fallback to spot account
            account_info = self._make_request('GET', '/sapi/v1/margin/account', signed=True)
            if not account_info:
                # Fallback to spot account
                account_info = self._make_request('GET', '/api/v3/account', signed=True)
                if not account_info:
                    return None

                # Convert spot account to margin-like format
                balances = account_info.get('balances', [])
                btc_balance = next((b for b in balances if b['asset'] == 'BTC'), {'free': '0', 'locked': '0'})
                usdt_balance = next((b for b in balances if b['asset'] == 'USDT'), {'free': '0', 'locked': '0'})

                # Get current BTC price
                btc_price = self.get_ticker_price('BTCUSDT')
                if not btc_price:
                    return None

                # Calculate portfolio values
                btc_free = float(btc_balance['free'])
                usdt_free = float(usdt_balance['free'])
                btc_value_usd = btc_free * btc_price
                total_value_usd = btc_value_usd + usdt_free

                # Create margin-like response for spot account
                return {
                    'info': {
                        'marginLevel': '999.0',  # High margin level for spot
                        'totalAssetOfBtc': str(total_value_usd / btc_price),
                        'totalLiabilityOfBtc': '0',
                        'totalNetAssetOfBtc': str(total_value_usd / btc_price),
                        'userAssets': [
                            {
                                'asset': 'BTC',
                                'free': str(btc_free),
                                'locked': '0',
                                'borrowed': '0',
                                'interest': '0',
                                'netAsset': str(btc_free)
                            },
                            {
                                'asset': 'USDT',
                                'free': str(usdt_free),
                                'locked': '0',
                                'borrowed': '0',
                                'interest': '0',
                                'netAsset': str(usdt_free)
                            }
                        ]
                    }
                }
            else:
                # Return margin account info
                return {'info': account_info}

        except Exception as e:
            print(f"❌ Error getting portfolio balance: {e}")
            return None
    
    def get_ticker_price(self, symbol: str = 'BTCUSDT') -> Optional[float]:
        """Get current ticker price."""
        try:
            result = self._make_request('GET', '/api/v3/ticker/price', {'symbol': symbol})
            if result:
                return float(result['price'])
            return None
            
        except Exception as e:
            print(f"❌ Error getting ticker price: {e}")
            return None
    
    def place_order(self, symbol: str, side: str, order_type: str, quantity: float, price: float = None) -> Optional[Dict]:
        """Place a trading order."""
        try:
            if not self.is_connected:
                if not self.test_connection():
                    return None
            
            params = {
                'symbol': symbol,
                'side': side.upper(),
                'type': order_type.upper(),
                'quantity': quantity
            }
            
            if price and order_type.upper() == 'LIMIT':
                params['price'] = price
                params['timeInForce'] = 'GTC'
            
            # For simulation, just return a mock order
            print(f"📝 SIMULATED ORDER: {side} {quantity} {symbol} at {price or 'market'}")
            return {
                'orderId': int(time.time()),
                'symbol': symbol,
                'side': side,
                'type': order_type,
                'quantity': quantity,
                'price': price,
                'status': 'FILLED',
                'executedQty': quantity
            }
            
            # Uncomment for real trading:
            # return self._make_request('POST', '/api/v3/order', params, signed=True)
            
        except Exception as e:
            print(f"❌ Error placing order: {e}")
            return None
    
    def place_margin_order(self, symbol: str, side: str, order_type: str, quantity: float, price: float = None) -> Optional[Dict]:
        """Place a margin trading order."""
        try:
            if not self.is_connected:
                if not self.test_connection():
                    return None
            
            params = {
                'symbol': symbol,
                'side': side.upper(),
                'type': order_type.upper(),
                'quantity': quantity
            }
            
            if price and order_type.upper() == 'LIMIT':
                params['price'] = price
                params['timeInForce'] = 'GTC'
            
            # For simulation, just return a mock order
            print(f"📝 SIMULATED MARGIN ORDER: {side} {quantity} {symbol} at {price or 'market'}")
            return {
                'orderId': int(time.time()),
                'symbol': symbol,
                'side': side,
                'type': order_type,
                'quantity': quantity,
                'price': price,
                'status': 'FILLED',
                'executedQty': quantity,
                'isMarginTrade': True
            }
            
            # Uncomment for real trading:
            # return self._make_request('POST', '/sapi/v1/margin/order', params, signed=True)
            
        except Exception as e:
            print(f"❌ Error placing margin order: {e}")
            return None
    
    def get_order_status(self, symbol: str, order_id: int) -> Optional[Dict]:
        """Get order status."""
        try:
            if not self.is_connected:
                if not self.test_connection():
                    return None
            
            params = {
                'symbol': symbol,
                'orderId': order_id
            }
            
            return self._make_request('GET', '/api/v3/order', params, signed=True)
            
        except Exception as e:
            print(f"❌ Error getting order status: {e}")
            return None

def create_simple_binance_connector(api_key_file: str, testnet: bool = False) -> Optional[SimpleBinanceConnector]:
    """Create a simple Binance connector from API key file."""
    try:
        with open(api_key_file, 'r') as f:
            lines = [line.strip() for line in f.readlines() if line.strip()]
        
        if len(lines) >= 2:
            api_key = lines[0]
            secret_key = lines[1]
            
            connector = SimpleBinanceConnector(api_key, secret_key, testnet)
            
            # Test connection
            if connector.test_connection():
                print("✅ Simple Binance Connector ready for trading")
                return connector
            else:
                print("❌ Simple Binance Connector connection failed")
                return None
        else:
            print("❌ Invalid API key file format")
            return None
            
    except Exception as e:
        print(f"❌ Error creating Simple Binance Connector: {e}")
        return None

if __name__ == "__main__":
    # Test the simple connector
    api_key_file = r"C:\Users\<USER>\Documents\Trading system\Trading project VPS 5\BinanceAPI_2.txt"
    
    print("🧪 Testing Simple Binance Connector")
    print("=" * 50)
    
    # Test with testnet first
    connector = create_simple_binance_connector(api_key_file, testnet=True)
    
    if connector:
        print("\n📊 Testing features:")
        
        # Test price fetching
        btc_price = connector.get_ticker_price('BTCUSDT')
        if btc_price:
            print(f"✅ BTC Price: ${btc_price:,.2f}")
        
        # Test account balance
        balance = connector.get_account_balance()
        if balance:
            print("✅ Account balance retrieved")
        
        # Test margin account
        margin = connector.get_margin_account()
        if margin:
            print("✅ Margin account retrieved")
        
        print("\n🚀 Simple Binance Connector is working!")
    else:
        print("\n❌ Simple Binance Connector failed to initialize")
