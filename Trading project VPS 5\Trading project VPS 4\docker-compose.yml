version: '3.8'

services:
  conservative-elite-trading:
    build: .
    container_name: conservative-elite-trading
    restart: unless-stopped
    ports:
      - "5000:5000"
    environment:
      - PYTHONUNBUFFERED=1
      - FLASK_ENV=production
      - BINANCE_API_KEY=${BINANCE_API_KEY}
      - BINANCE_SECRET_KEY=${BINANCE_SECRET_KEY}
      - BINANCE_TESTNET=${BINANCE_TESTNET:-false}
      - TRADING_MODE=live
    volumes:
      - ./data:/app/data
      - ./logs:/app/logs
      - ./models:/app/models
      - ./cache:/app/cache
      - ./config:/app/config
      - trading_db:/app/database
    networks:
      - trading-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

volumes:
  trading_db:
    driver: local

networks:
  trading-network:
    driver: bridge
