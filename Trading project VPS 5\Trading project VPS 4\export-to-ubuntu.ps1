# Conservative Elite Trading System - Export to Ubuntu VPS (PowerShell)
# This script packages the system for transfer to Ubuntu VPS

Write-Host "📦 CONSERVATIVE ELITE TRADING SYSTEM - EXPORT PACKAGE" -ForegroundColor Green
Write-Host "=====================================================" -ForegroundColor Green

# Create export directory with timestamp
$timestamp = Get-Date -Format "yyyyMMdd_HHmmss"
$exportDir = "conservative-elite-export-$timestamp"

Write-Host "ℹ️ Creating export package: $exportDir" -ForegroundColor Blue
New-Item -ItemType Directory -Path $exportDir -Force | Out-Null

Write-Host "ℹ️ Copying essential application files..." -ForegroundColor Blue

# Core application files
$coreFiles = @(
    "production_live_trading_app.py",
    "live_trading_web_app.py",
    "simple_binance_connector.py",
    "trade_csv_logger.py",
    "trade_database.py",
    "robust_metrics_calculator.py",
    "data_cache_manager.py",
    "ai_signal_monitor.py",
    "enhanced_retraining_system_tcn_cnn_ppo.py",
    "simplified_tcn_cnn_ppo_trainer.py",
    "requirements.txt",
    "Dockerfile",
    "docker-compose.yml",
    ".env.example",
    ".dockerignore",
    "deploy-ubuntu.sh",
    "export-to-ubuntu.sh"
)

foreach ($file in $coreFiles) {
    if (Test-Path $file) {
        Copy-Item $file $exportDir -Force
        Write-Host "✅ Copied: $file" -ForegroundColor Green
    } else {
        Write-Host "⚠️ Missing: $file" -ForegroundColor Yellow
    }
}

# Copy templates directory
Write-Host "ℹ️ Copying templates..." -ForegroundColor Blue
if (Test-Path "templates") {
    Copy-Item "templates" $exportDir -Recurse -Force
    Write-Host "✅ Templates copied" -ForegroundColor Green
}

# Copy models directory (essential models only)
Write-Host "ℹ️ Copying AI models..." -ForegroundColor Blue
New-Item -ItemType Directory -Path "$exportDir\models" -Force | Out-Null
if (Test-Path "models") {
    # Copy Conservative Elite and TCN-CNN-PPO models
    Get-ChildItem "models" -Filter "*conservative*" | Copy-Item -Destination "$exportDir\models" -Force
    Get-ChildItem "models" -Filter "*tcn_cnn_ppo*" | Copy-Item -Destination "$exportDir\models" -Force
    Get-ChildItem "models" -Filter "*.json" | Copy-Item -Destination "$exportDir\models" -Force
    Write-Host "✅ AI models copied" -ForegroundColor Green
}

# Copy config directory
Write-Host "ℹ️ Copying configuration..." -ForegroundColor Blue
if (Test-Path "config") {
    Copy-Item "config" $exportDir -Recurse -Force
    Write-Host "✅ Configuration copied" -ForegroundColor Green
}

# Create empty runtime directories
Write-Host "ℹ️ Creating runtime directories..." -ForegroundColor Blue
@("logs", "cache", "data") | ForEach-Object {
    New-Item -ItemType Directory -Path "$exportDir\$_" -Force | Out-Null
}

# Create README for deployment
Write-Host "ℹ️ Creating deployment README..." -ForegroundColor Blue
$readmeContent = @"
# 🏆 Conservative Elite Trading System - Ubuntu Deployment

## 🚀 Quick Start

1. **Upload to your Ubuntu VPS**:
   ``````bash
   scp -r $exportDir user@your-vps-ip:~/
   ``````

2. **Connect to your VPS**:
   ``````bash
   ssh user@your-vps-ip
   cd $exportDir
   ``````

3. **Run deployment script**:
   ``````bash
   chmod +x deploy-ubuntu.sh
   ./deploy-ubuntu.sh
   ``````

4. **Configure API credentials**:
   ``````bash
   nano .env
   # Add your Binance API key and secret
   ``````

5. **Start the system**:
   ``````bash
   podman-compose up -d
   ``````

6. **Access dashboard**:
   ``````
   http://your-vps-ip:5000
   ``````

## 🔧 Management Commands

- **Start**: ``podman-compose up -d``
- **Stop**: ``podman-compose down``
- **Logs**: ``podman logs conservative-elite-trading``
- **Monitor**: ``./monitor.sh``
- **Restart**: ``podman-compose restart``

## 📊 System Features

- ✅ **Conservative Elite AI**: 93.2% Win Rate
- ✅ **Real-time Trading**: Live Binance integration
- ✅ **Auto-restart**: Systemd service integration
- ✅ **Monitoring**: Built-in health checks
- ✅ **Security**: Non-root container execution

## ⚠️ Important Notes

1. **API Security**: Keep your .env file secure
2. **Firewall**: Port 5000 will be opened automatically
3. **Resources**: Minimum 2GB RAM recommended
4. **Backup**: Regular database backups recommended
"@

Set-Content -Path "$exportDir\README.md" -Value $readmeContent

# Create quick start script
$quickStartContent = @"
#!/bin/bash
echo "🚀 Conservative Elite Trading System - Quick Start"
echo "================================================="

# Check if .env exists
if [ ! -f .env ]; then
    echo "⚠️ Creating .env from template..."
    cp .env.example .env
    echo "❗ IMPORTANT: Edit .env with your Binance API credentials!"
    echo "   Run: nano .env"
    echo ""
fi

# Start with podman-compose
echo "🔄 Starting Conservative Elite Trading System..."
podman-compose up -d

echo ""
echo "✅ System started! Access at: http://localhost:5000"
echo "📊 Monitor with: ./monitor.sh"
echo "🛑 Stop with: podman-compose down"
"@

Set-Content -Path "$exportDir\quick-start.sh" -Value $quickStartContent

# Create compressed archive
Write-Host "ℹ️ Creating compressed archive..." -ForegroundColor Blue
$archiveName = "$exportDir.zip"
Compress-Archive -Path $exportDir -DestinationPath $archiveName -Force

# Calculate sizes
$folderSize = (Get-ChildItem $exportDir -Recurse | Measure-Object -Property Length -Sum).Sum / 1MB
$archiveSize = (Get-Item $archiveName).Length / 1MB

Write-Host "✅ Export package created successfully!" -ForegroundColor Green
Write-Host ""
Write-Host "📦 PACKAGE DETAILS:" -ForegroundColor Blue
Write-Host "   Folder: $exportDir ($([math]::Round($folderSize, 2)) MB)"
Write-Host "   Archive: $archiveName ($([math]::Round($archiveSize, 2)) MB)"
Write-Host ""
Write-Host "🚀 DEPLOYMENT INSTRUCTIONS:" -ForegroundColor Blue
Write-Host "1. Upload to VPS: scp $archiveName user@vps-ip:~/"
Write-Host "2. Extract: unzip $archiveName"
Write-Host "3. Deploy: cd $exportDir && ./deploy-ubuntu.sh"
Write-Host ""
Write-Host "⚠️ REMEMBER: Configure .env with your Binance API credentials!" -ForegroundColor Yellow
Write-Host "⚠️ SECURITY: Keep API credentials secure!" -ForegroundColor Yellow

Write-Host "✅ Ready for Ubuntu VPS deployment! 🎯" -ForegroundColor Green
