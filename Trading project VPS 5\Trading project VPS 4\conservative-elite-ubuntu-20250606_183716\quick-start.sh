#!/bin/bash 
echo "🚀 Conservative Elite Trading System - Quick Start" 
echo "=================================================" 
echo "" 
if [ ! -f .env ]; then 
    echo "⚠️ Creating .env from template..." 
    cp .env.example .env 
    echo "❗ IMPORTANT: Edit .env with your Binance API credentials!" 
    echo "   Run: nano .env" 
    echo "" 
fi 
echo "🔄 Starting Conservative Elite Trading System..." 
podman-compose up -d 
echo "" 
echo "✅ System started! Access at: http://localhost:5000" 
echo "📊 Monitor with: ./monitor.sh" 
echo "🛑 Stop with: podman-compose down" 
