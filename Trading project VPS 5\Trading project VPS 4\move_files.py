#!/usr/bin/env python3
"""
Simple file mover for refactoring
"""

import os
import shutil
from pathlib import Path

def move_documentation_files():
    """Move all .md files to archive"""
    source_dir = Path(".")
    archive_dir = Path("C:/Users/<USER>/Documents/Live Trading Systems Production/Trading project VPS 5/Archive/refactor_archive_20250606_124640/documentation")
    
    archive_dir.mkdir(parents=True, exist_ok=True)
    
    moved_count = 0
    for file in source_dir.glob("*.md"):
        try:
            shutil.move(str(file), str(archive_dir / file.name))
            print(f"✅ Moved: {file.name}")
            moved_count += 1
        except Exception as e:
            print(f"❌ Error moving {file.name}: {e}")
    
    print(f"📊 Moved {moved_count} documentation files")

def move_test_files():
    """Move all test files to archive"""
    source_dir = Path(".")
    archive_dir = Path("C:/Users/<USER>/Documents/Live Trading Systems Production/Trading project VPS 5/Archive/refactor_archive_20250606_124640/test_files")
    
    archive_dir.mkdir(parents=True, exist_ok=True)
    
    moved_count = 0
    for file in source_dir.glob("test_*"):
        try:
            if file.is_file():
                shutil.move(str(file), str(archive_dir / file.name))
                print(f"✅ Moved: {file.name}")
                moved_count += 1
        except Exception as e:
            print(f"❌ Error moving {file.name}: {e}")
    
    print(f"📊 Moved {moved_count} test files")

def move_launcher_files():
    """Move launcher files (except production app)"""
    source_dir = Path(".")
    archive_dir = Path("C:/Users/<USER>/Documents/Live Trading Systems Production/Trading project VPS 5/Archive/refactor_archive_20250606_124640/launchers")
    
    archive_dir.mkdir(parents=True, exist_ok=True)
    
    launcher_patterns = ["launch_*", "deploy_*", "start_*"]
    essential_files = ["production_live_trading_app.py"]
    
    moved_count = 0
    for pattern in launcher_patterns:
        for file in source_dir.glob(pattern):
            if file.is_file() and file.name not in essential_files:
                try:
                    shutil.move(str(file), str(archive_dir / file.name))
                    print(f"✅ Moved: {file.name}")
                    moved_count += 1
                except Exception as e:
                    print(f"❌ Error moving {file.name}: {e}")
    
    print(f"📊 Moved {moved_count} launcher files")

def move_backup_files():
    """Move backup files and directories"""
    source_dir = Path(".")
    archive_dir = Path("C:/Users/<USER>/Documents/Live Trading Systems Production/Trading project VPS 5/Archive/refactor_archive_20250606_124640/backup_files")
    
    archive_dir.mkdir(parents=True, exist_ok=True)
    
    moved_count = 0
    
    # Move backup files
    for file in source_dir.glob("*backup*"):
        try:
            if file.is_file():
                shutil.move(str(file), str(archive_dir / file.name))
                print(f"✅ Moved: {file.name}")
                moved_count += 1
        except Exception as e:
            print(f"❌ Error moving {file.name}: {e}")
    
    # Move backups directory
    backups_dir = source_dir / "backups"
    if backups_dir.exists():
        try:
            shutil.move(str(backups_dir), str(archive_dir / "backups"))
            print(f"✅ Moved directory: backups")
            moved_count += 1
        except Exception as e:
            print(f"❌ Error moving backups directory: {e}")
    
    print(f"📊 Moved {moved_count} backup items")

def move_analysis_files():
    """Move analysis and check files"""
    source_dir = Path(".")
    archive_dir = Path("C:/Users/<USER>/Documents/Live Trading Systems Production/Trading project VPS 5/Archive/refactor_archive_20250606_124640/analysis_tools")
    
    archive_dir.mkdir(parents=True, exist_ok=True)
    
    analysis_patterns = ["analyze_*", "check_*", "audit_*", "verify_*", "comprehensive_*"]
    
    moved_count = 0
    for pattern in analysis_patterns:
        for file in source_dir.glob(pattern):
            if file.is_file():
                try:
                    shutil.move(str(file), str(archive_dir / file.name))
                    print(f"✅ Moved: {file.name}")
                    moved_count += 1
                except Exception as e:
                    print(f"❌ Error moving {file.name}: {e}")
    
    print(f"📊 Moved {moved_count} analysis files")

if __name__ == "__main__":
    print("🚀 STARTING TARGETED FILE MOVING")
    print("=" * 50)
    
    print("\n📄 Moving documentation files...")
    move_documentation_files()
    
    print("\n🧪 Moving test files...")
    move_test_files()
    
    print("\n🚀 Moving launcher files...")
    move_launcher_files()
    
    print("\n💾 Moving backup files...")
    move_backup_files()
    
    print("\n🔍 Moving analysis files...")
    move_analysis_files()
    
    print("\n✅ TARGETED MOVING COMPLETE")
