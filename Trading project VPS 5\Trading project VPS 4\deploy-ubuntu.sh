#!/bin/bash

# Conservative Elite Trading System - Ubuntu VPS Deployment Script
# This script deploys the system using Podman on Ubuntu

set -e

echo "🚀 CONSERVATIVE ELITE TRADING SYSTEM - UBUNTU DEPLOYMENT"
echo "=========================================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️ $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️ $1${NC}"
}

# Check if running as root
if [[ $EUID -eq 0 ]]; then
   print_error "This script should not be run as root for security reasons"
   exit 1
fi

# Update system
print_info "Updating system packages..."
sudo apt update && sudo apt upgrade -y

# Install Podman
print_info "Installing Podman..."
sudo apt install -y podman podman-compose

# Install additional tools
print_info "Installing additional tools..."
sudo apt install -y curl wget git htop nano

# Verify Podman installation
print_info "Verifying Podman installation..."
podman --version
podman-compose --version

# Create application directory
APP_DIR="$HOME/conservative-elite-trading"
print_info "Creating application directory: $APP_DIR"
mkdir -p "$APP_DIR"
cd "$APP_DIR"

# Create necessary subdirectories
print_info "Creating subdirectories..."
mkdir -p data logs models cache config

# Copy .env.example to .env if it doesn't exist
if [ ! -f .env ]; then
    print_info "Creating .env file from template..."
    cp .env.example .env
    print_warning "Please edit .env file with your Binance API credentials!"
    print_warning "Run: nano .env"
fi

# Set proper permissions
print_info "Setting permissions..."
chmod 755 deploy-ubuntu.sh
chmod 600 .env

# Build the container
print_info "Building Conservative Elite container..."
podman build -t conservative-elite-trading .

# Create systemd service for auto-start
print_info "Creating systemd service..."
mkdir -p ~/.config/systemd/user

cat > ~/.config/systemd/user/conservative-elite-trading.service << EOF
[Unit]
Description=Conservative Elite Trading System
After=network.target

[Service]
Type=simple
WorkingDirectory=$APP_DIR
ExecStart=/usr/bin/podman-compose up
ExecStop=/usr/bin/podman-compose down
Restart=always
RestartSec=10

[Install]
WantedBy=default.target
EOF

# Enable and start the service
print_info "Enabling systemd service..."
systemctl --user daemon-reload
systemctl --user enable conservative-elite-trading.service

# Configure firewall
print_info "Configuring firewall..."
sudo ufw allow 5000/tcp
sudo ufw --force enable

# Create monitoring script
print_info "Creating monitoring script..."
cat > monitor.sh << 'EOF'
#!/bin/bash
echo "🔍 CONSERVATIVE ELITE TRADING SYSTEM STATUS"
echo "==========================================="
echo "📊 Container Status:"
podman ps --filter name=conservative-elite-trading
echo ""
echo "📈 System Resources:"
echo "CPU Usage: $(top -bn1 | grep "Cpu(s)" | awk '{print $2}' | cut -d'%' -f1)%"
echo "Memory Usage: $(free -m | awk 'NR==2{printf "%.1f%%", $3*100/$2}')"
echo "Disk Usage: $(df -h / | awk 'NR==2{print $5}')"
echo ""
echo "🌐 Service Status:"
curl -s http://localhost:5000/health || echo "Service not responding"
echo ""
echo "📝 Recent Logs:"
podman logs --tail 10 conservative-elite-trading
EOF

chmod +x monitor.sh

print_status "Deployment completed successfully!"
echo ""
print_info "🎯 NEXT STEPS:"
echo "1. Edit your API credentials: nano .env"
echo "2. Start the system: podman-compose up -d"
echo "3. Monitor the system: ./monitor.sh"
echo "4. Access dashboard: http://your-vps-ip:5000"
echo ""
print_info "🔧 USEFUL COMMANDS:"
echo "• Start: podman-compose up -d"
echo "• Stop: podman-compose down"
echo "• Logs: podman logs conservative-elite-trading"
echo "• Monitor: ./monitor.sh"
echo "• Restart: podman-compose restart"
echo ""
print_warning "⚠️ IMPORTANT: Update .env with your real Binance API credentials!"
print_warning "⚠️ SECURITY: Keep your .env file secure and never share it!"
