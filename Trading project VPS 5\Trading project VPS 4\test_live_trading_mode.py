#!/usr/bin/env python3
"""
Live Trading Mode Test Script
============================
Tests the live trading mode switch functionality
"""

import requests
import json
import time

def test_live_trading_mode():
    base_url = "http://localhost:5000"
    
    print("🧪 LIVE TRADING MODE TEST")
    print("=" * 50)
    
    # Test 1: Check current status
    print("\n1️⃣ Checking current trading status...")
    try:
        response = requests.get(f"{base_url}/api/trading_status", timeout=5)
        if response.status_code == 200:
            data = response.json()
            current_live_mode = data.get('is_live_mode', False)
            binance_connected = data.get('binance_connected', False)
            print(f"✅ Current Status:")
            print(f"   Live Mode: {current_live_mode}")
            print(f"   Binance Connected: {binance_connected}")
        else:
            print(f"❌ Failed to get trading status: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Error getting trading status: {e}")
        return False
    
    # Test 2: Test Binance connection status
    print("\n2️⃣ Testing Binance connection status...")
    try:
        response = requests.get(f"{base_url}/api/binance_status", timeout=10)
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Binance Status:")
            print(f"   Connected: {data.get('connected', False)}")
            print(f"   Testnet: {data.get('testnet', 'Unknown')}")
            print(f"   Connector Type: {data.get('connector_type', 'Unknown')}")
            print(f"   BTC Price: ${data.get('current_btc_price', 0):,.2f}")
        else:
            print(f"❌ Failed to get Binance status: {response.status_code}")
    except Exception as e:
        print(f"❌ Error getting Binance status: {e}")
    
    # Test 3: Test toggle to TESTNET live mode
    print("\n3️⃣ Testing toggle to TESTNET live mode...")
    try:
        response = requests.post(f"{base_url}/api/toggle_live_mode", 
                               json={
                                   'live_mode': True,
                                   'testnet': True,
                                   'use_margin': False
                               }, 
                               timeout=15)
        if response.status_code == 200:
            data = response.json()
            if data.get('status') == 'success':
                print(f"✅ TESTNET Live Mode: SUCCESS")
                print(f"   Message: {data.get('message', 'No message')}")
                print(f"   Live Mode: {data.get('live_mode', False)}")
                print(f"   Testnet: {data.get('testnet', 'Unknown')}")
                print(f"   Connected: {data.get('connected', False)}")
            else:
                print(f"⚠️ TESTNET Live Mode: {data.get('message', 'Unknown error')}")
        else:
            print(f"❌ TESTNET toggle failed: {response.status_code}")
    except Exception as e:
        print(f"❌ Error testing TESTNET mode: {e}")
    
    # Test 4: Test toggle to REAL MONEY live mode (with warning)
    print("\n4️⃣ Testing toggle to REAL MONEY live mode...")
    print("⚠️ WARNING: This will attempt to connect to LIVE Binance API")
    print("⚠️ This is for testing purposes only - no trades will be executed")
    
    user_confirm = input("Continue with REAL MONEY mode test? (y/N): ").lower()
    if user_confirm == 'y':
        try:
            response = requests.post(f"{base_url}/api/toggle_live_mode", 
                                   json={
                                       'live_mode': True,
                                       'testnet': False,  # REAL MONEY
                                       'use_margin': False
                                   }, 
                                   timeout=15)
            if response.status_code == 200:
                data = response.json()
                if data.get('status') == 'success':
                    print(f"✅ REAL MONEY Live Mode: SUCCESS")
                    print(f"   Message: {data.get('message', 'No message')}")
                    print(f"   Live Mode: {data.get('live_mode', False)}")
                    print(f"   Testnet: {data.get('testnet', 'Unknown')}")
                    print(f"   Connected: {data.get('connected', False)}")
                    print(f"🚨 REAL MONEY MODE ACTIVE - BE CAREFUL!")
                else:
                    print(f"⚠️ REAL MONEY Live Mode: {data.get('message', 'Unknown error')}")
            else:
                print(f"❌ REAL MONEY toggle failed: {response.status_code}")
        except Exception as e:
            print(f"❌ Error testing REAL MONEY mode: {e}")
    else:
        print("⏭️ Skipping REAL MONEY mode test")
    
    # Test 5: Test toggle back to simulation mode
    print("\n5️⃣ Testing toggle back to simulation mode...")
    try:
        response = requests.post(f"{base_url}/api/toggle_live_mode", 
                               json={'live_mode': False}, 
                               timeout=10)
        if response.status_code == 200:
            data = response.json()
            if data.get('status') == 'success':
                print(f"✅ Simulation Mode: SUCCESS")
                print(f"   Message: {data.get('message', 'No message')}")
                print(f"   Live Mode: {data.get('live_mode', True)}")
            else:
                print(f"⚠️ Simulation Mode: {data.get('message', 'Unknown error')}")
        else:
            print(f"❌ Simulation toggle failed: {response.status_code}")
    except Exception as e:
        print(f"❌ Error testing simulation mode: {e}")
    
    # Test 6: Final status check
    print("\n6️⃣ Final status check...")
    try:
        response = requests.get(f"{base_url}/api/trading_status", timeout=5)
        if response.status_code == 200:
            data = response.json()
            final_live_mode = data.get('is_live_mode', False)
            final_binance_connected = data.get('binance_connected', False)
            print(f"✅ Final Status:")
            print(f"   Live Mode: {final_live_mode}")
            print(f"   Binance Connected: {final_binance_connected}")
        else:
            print(f"❌ Failed to get final status: {response.status_code}")
    except Exception as e:
        print(f"❌ Error getting final status: {e}")
    
    print("\n" + "=" * 50)
    print("🎯 LIVE TRADING MODE TEST COMPLETE!")
    print("🌐 Dashboard URL: http://localhost:5000")
    print("💡 Use the 'Switch to Live Mode' button to test the frontend")
    print("=" * 50)

if __name__ == "__main__":
    test_live_trading_mode()
