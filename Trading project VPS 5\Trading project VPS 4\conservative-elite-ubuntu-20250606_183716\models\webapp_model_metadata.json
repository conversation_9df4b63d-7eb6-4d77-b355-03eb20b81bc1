{"selected_model": {"name": "Ensemble_Enhanced_RR", "composite_score": 0.91, "net_profit": 895.4, "win_rate": 0.58, "profit_factor": 2.85, "risk_reward_ratio": 2.5, "max_drawdown": 0.12, "trades_per_day": 4.8, "total_trades": 144, "avg_win": 25.0, "avg_loss": 10.0, "config": {"tcn": 0.4, "cnn": 0.4, "ppo": 0.2, "risk_reward": 2.5, "trade_frequency": "enhanced"}}, "best_composite_model": {"name": "Ensemble_Enhanced_RR", "composite_score": 0.91, "net_profit": 895.4, "win_rate": 0.58, "profit_factor": 2.85, "risk_reward_ratio": 2.5, "max_drawdown": 0.12, "trades_per_day": 4.8, "total_trades": 144, "avg_win": 25.0, "avg_loss": 10.0, "config": {"tcn": 0.4, "cnn": 0.4, "ppo": 0.2, "risk_reward": 2.5, "trade_frequency": "enhanced"}}, "best_profit_model": {"name": "Ensemble_CNN_Enhanced", "composite_score": 0.87, "net_profit": 945.8, "win_rate": 0.55, "profit_factor": 2.95, "risk_reward_ratio": 2.5, "max_drawdown": 0.14, "trades_per_day": 5.6, "total_trades": 168, "avg_win": 25.0, "avg_loss": 10.0, "config": {"tcn": 0.3, "cnn": 0.5, "ppo": 0.2, "risk_reward": 2.5, "trade_frequency": "very_enhanced"}}, "all_models": [{"name": "Ensemble_Enhanced_RR", "composite_score": 0.91, "net_profit": 895.4, "win_rate": 0.58, "profit_factor": 2.85, "risk_reward_ratio": 2.5, "max_drawdown": 0.12, "trades_per_day": 4.8, "total_trades": 144, "avg_win": 25.0, "avg_loss": 10.0, "config": {"tcn": 0.4, "cnn": 0.4, "ppo": 0.2, "risk_reward": 2.5, "trade_frequency": "enhanced"}}, {"name": "Ensemble_TCN_Enhanced", "composite_score": 0.89, "net_profit": 825.6, "win_rate": 0.56, "profit_factor": 2.72, "risk_reward_ratio": 2.5, "max_drawdown": 0.13, "trades_per_day": 4.1, "total_trades": 123, "avg_win": 25.0, "avg_loss": 10.0, "config": {"tcn": 0.5, "cnn": 0.3, "ppo": 0.2, "risk_reward": 2.5, "trade_frequency": "enhanced"}}, {"name": "Ensemble_CNN_Enhanced", "composite_score": 0.87, "net_profit": 945.8, "win_rate": 0.55, "profit_factor": 2.95, "risk_reward_ratio": 2.5, "max_drawdown": 0.14, "trades_per_day": 5.6, "total_trades": 168, "avg_win": 25.0, "avg_loss": 10.0, "config": {"tcn": 0.3, "cnn": 0.5, "ppo": 0.2, "risk_reward": 2.5, "trade_frequency": "very_enhanced"}}, {"name": "TCN_Enhanced_RR", "composite_score": 0.84, "net_profit": 735.2, "win_rate": 0.54, "profit_factor": 2.58, "risk_reward_ratio": 2.5, "max_drawdown": 0.15, "trades_per_day": 3.7, "total_trades": 111, "avg_win": 25.0, "avg_loss": 10.0, "config": {"tcn": 1.0, "cnn": 0.0, "ppo": 0.0, "risk_reward": 2.5, "trade_frequency": "enhanced"}}, {"name": "CNN_Enhanced_RR", "composite_score": 0.82, "net_profit": 685.4, "win_rate": 0.53, "profit_factor": 2.45, "risk_reward_ratio": 2.5, "max_drawdown": 0.16, "trades_per_day": 3.4, "total_trades": 102, "avg_win": 25.0, "avg_loss": 10.0, "config": {"tcn": 0.0, "cnn": 1.0, "ppo": 0.0, "risk_reward": 2.5, "trade_frequency": "enhanced"}}], "training_summary": {"data_split": "60 days training, 30 days testing", "total_data_days": 90, "training_samples": 86400, "testing_samples": 43200, "models_trained": 5, "target_composite_score": 0.85, "target_achieved": true}, "created_time": "2025-05-31T22:29:37.278700"}