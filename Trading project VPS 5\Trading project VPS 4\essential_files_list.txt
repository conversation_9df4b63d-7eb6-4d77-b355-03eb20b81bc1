# Essential Files for Production Live Trading App
# These files must be kept in the main directory

# Core Application
production_live_trading_app.py
live_trading_web_app.py

# Core Trading Components
simple_binance_connector.py
trade_csv_logger.py
trade_database.py
robust_metrics_calculator.py
data_cache_manager.py
ai_signal_monitor.py
enhanced_retraining_system_tcn_cnn_ppo.py
simplified_tcn_cnn_ppo_trainer.py

# Configuration
requirements.txt

# Data Files (keep existing data)
bitcoin_freedom_trades.db
bitcoin_freedom_trades.db-shm
bitcoin_freedom_trades.db-wal
trade_history.csv

# Essential Directories (keep entire directories)
templates/
models/
logs/
cache/
config/
__pycache__/

# Essential Template Files
templates/bitcoin_freedom_dashboard.html
templates/production_live_trading_dashboard.html

# Essential Config Files
config/trading_config.py
