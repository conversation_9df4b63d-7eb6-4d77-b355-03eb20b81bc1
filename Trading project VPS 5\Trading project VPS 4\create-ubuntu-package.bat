@echo off
echo 📦 CONSERVATIVE ELITE TRADING SYSTEM - UBUNTU PACKAGE CREATOR
echo ==============================================================

set TIMESTAMP=%date:~-4,4%%date:~-10,2%%date:~-7,2%_%time:~0,2%%time:~3,2%%time:~6,2%
set TIMESTAMP=%TIMESTAMP: =0%
set PACKAGE_NAME=conservative-elite-ubuntu-%TIMESTAMP%

echo ℹ️ Creating package: %PACKAGE_NAME%
mkdir "%PACKAGE_NAME%"

echo ℹ️ Copying essential files...

REM Core application files
copy "production_live_trading_app.py" "%PACKAGE_NAME%\" >nul 2>&1
copy "live_trading_web_app.py" "%PACKAGE_NAME%\" >nul 2>&1
copy "simple_binance_connector.py" "%PACKAGE_NAME%\" >nul 2>&1
copy "trade_csv_logger.py" "%PACKAGE_NAME%\" >nul 2>&1
copy "trade_database.py" "%PACKAGE_NAME%\" >nul 2>&1
copy "robust_metrics_calculator.py" "%PACKAGE_NAME%\" >nul 2>&1
copy "data_cache_manager.py" "%PACKAGE_NAME%\" >nul 2>&1
copy "ai_signal_monitor.py" "%PACKAGE_NAME%\" >nul 2>&1
copy "enhanced_retraining_system_tcn_cnn_ppo.py" "%PACKAGE_NAME%\" >nul 2>&1
copy "simplified_tcn_cnn_ppo_trainer.py" "%PACKAGE_NAME%\" >nul 2>&1

REM Configuration files
copy "requirements.txt" "%PACKAGE_NAME%\" >nul 2>&1
copy "Dockerfile" "%PACKAGE_NAME%\" >nul 2>&1
copy "docker-compose.yml" "%PACKAGE_NAME%\" >nul 2>&1
copy ".env.example" "%PACKAGE_NAME%\" >nul 2>&1
copy ".dockerignore" "%PACKAGE_NAME%\" >nul 2>&1
copy "deploy-ubuntu.sh" "%PACKAGE_NAME%\" >nul 2>&1
copy "UBUNTU_DEPLOYMENT_GUIDE.md" "%PACKAGE_NAME%\" >nul 2>&1

echo ℹ️ Copying directories...

REM Copy templates
if exist "templates" (
    xcopy "templates" "%PACKAGE_NAME%\templates\" /E /I /Q >nul 2>&1
    echo ✅ Templates copied
)

REM Copy models (essential ones only)
if exist "models" (
    mkdir "%PACKAGE_NAME%\models" >nul 2>&1
    copy "models\*conservative*" "%PACKAGE_NAME%\models\" >nul 2>&1
    copy "models\*tcn_cnn_ppo*" "%PACKAGE_NAME%\models\" >nul 2>&1
    copy "models\*.json" "%PACKAGE_NAME%\models\" >nul 2>&1
    echo ✅ AI models copied
)

REM Copy config
if exist "config" (
    xcopy "config" "%PACKAGE_NAME%\config\" /E /I /Q >nul 2>&1
    echo ✅ Configuration copied
)

echo ℹ️ Creating runtime directories...
mkdir "%PACKAGE_NAME%\logs" >nul 2>&1
mkdir "%PACKAGE_NAME%\cache" >nul 2>&1
mkdir "%PACKAGE_NAME%\data" >nul 2>&1

echo ℹ️ Creating quick start script...
echo #!/bin/bash > "%PACKAGE_NAME%\quick-start.sh"
echo echo "🚀 Conservative Elite Trading System - Quick Start" >> "%PACKAGE_NAME%\quick-start.sh"
echo echo "=================================================" >> "%PACKAGE_NAME%\quick-start.sh"
echo echo "" >> "%PACKAGE_NAME%\quick-start.sh"
echo if [ ! -f .env ]; then >> "%PACKAGE_NAME%\quick-start.sh"
echo     echo "⚠️ Creating .env from template..." >> "%PACKAGE_NAME%\quick-start.sh"
echo     cp .env.example .env >> "%PACKAGE_NAME%\quick-start.sh"
echo     echo "❗ IMPORTANT: Edit .env with your Binance API credentials!" >> "%PACKAGE_NAME%\quick-start.sh"
echo     echo "   Run: nano .env" >> "%PACKAGE_NAME%\quick-start.sh"
echo     echo "" >> "%PACKAGE_NAME%\quick-start.sh"
echo fi >> "%PACKAGE_NAME%\quick-start.sh"
echo echo "🔄 Starting Conservative Elite Trading System..." >> "%PACKAGE_NAME%\quick-start.sh"
echo podman-compose up -d >> "%PACKAGE_NAME%\quick-start.sh"
echo echo "" >> "%PACKAGE_NAME%\quick-start.sh"
echo echo "✅ System started! Access at: http://localhost:5000" >> "%PACKAGE_NAME%\quick-start.sh"
echo echo "📊 Monitor with: ./monitor.sh" >> "%PACKAGE_NAME%\quick-start.sh"
echo echo "🛑 Stop with: podman-compose down" >> "%PACKAGE_NAME%\quick-start.sh"

echo ℹ️ Creating deployment instructions...
echo # Conservative Elite Trading System - Ubuntu Deployment > "%PACKAGE_NAME%\README.md"
echo. >> "%PACKAGE_NAME%\README.md"
echo ## Quick Start >> "%PACKAGE_NAME%\README.md"
echo. >> "%PACKAGE_NAME%\README.md"
echo 1. Upload this folder to your Ubuntu VPS >> "%PACKAGE_NAME%\README.md"
echo 2. Run: chmod +x deploy-ubuntu.sh >> "%PACKAGE_NAME%\README.md"
echo 3. Run: ./deploy-ubuntu.sh >> "%PACKAGE_NAME%\README.md"
echo 4. Edit .env with your Binance API credentials >> "%PACKAGE_NAME%\README.md"
echo 5. Run: podman-compose up -d >> "%PACKAGE_NAME%\README.md"
echo 6. Access: http://your-vps-ip:5000 >> "%PACKAGE_NAME%\README.md"
echo. >> "%PACKAGE_NAME%\README.md"
echo See UBUNTU_DEPLOYMENT_GUIDE.md for detailed instructions. >> "%PACKAGE_NAME%\README.md"

echo ✅ Package created successfully!
echo.
echo 📦 PACKAGE DETAILS:
echo    Folder: %PACKAGE_NAME%
echo    Location: %CD%\%PACKAGE_NAME%
echo.
echo 🚀 DEPLOYMENT INSTRUCTIONS:
echo 1. Compress %PACKAGE_NAME% folder to ZIP
echo 2. Upload ZIP to VPS: scp %PACKAGE_NAME%.zip user@vps-ip:~/
echo 3. Extract: unzip %PACKAGE_NAME%.zip
echo 4. Deploy: cd %PACKAGE_NAME% ^&^& ./deploy-ubuntu.sh
echo.
echo ⚠️ REMEMBER: Configure .env with your Binance API credentials!
echo ⚠️ SECURITY: Keep API credentials secure!
echo.
echo ✅ Ready for Ubuntu VPS deployment! 🎯

pause
