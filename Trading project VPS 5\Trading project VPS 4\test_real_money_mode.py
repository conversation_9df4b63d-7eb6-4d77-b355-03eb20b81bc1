#!/usr/bin/env python3
"""
Real Money Trading Mode Test Script
==================================
Tests the real money trading mode functionality
"""

import requests
import json
import time
from datetime import datetime

class RealMoneyTradingTester:
    def __init__(self, base_url="http://localhost:5000"):
        self.base_url = base_url
        self.test_results = []
        
    def log_test(self, test_name, success, message, data=None):
        """Log test results"""
        result = {
            'test': test_name,
            'success': success,
            'message': message,
            'timestamp': datetime.now().isoformat(),
            'data': data
        }
        self.test_results.append(result)
        
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status} {test_name}: {message}")
        if data and isinstance(data, dict):
            for key, value in data.items():
                print(f"   {key}: {value}")
    
    def test_webapp_running(self):
        """Test if webapp is running"""
        try:
            response = requests.get(f"{self.base_url}/health", timeout=5)
            if response.status_code == 200:
                data = response.json()
                self.log_test("WebApp Status", True, "WebApp is running", {
                    "status": data.get('status', 'unknown'),
                    "trading_engine": data.get('trading_engine', 'unknown')
                })
                return True
            else:
                self.log_test("WebApp Status", False, f"WebApp returned {response.status_code}")
                return False
        except Exception as e:
            self.log_test("WebApp Status", False, f"WebApp connection failed: {e}")
            return False
    
    def test_real_money_toggle(self):
        """Test real money trading mode toggle"""
        try:
            # Test activating real money mode
            response = requests.post(f"{self.base_url}/api/toggle_live_mode", 
                                   json={
                                       'live_mode': True,
                                       'use_margin': False
                                   }, 
                                   timeout=15)
            
            if response.status_code == 200:
                data = response.json()
                if data.get('status') == 'success':
                    self.log_test("Real Money Activation", True, "Real money mode activated", {
                        "live_mode": data.get('live_mode', False),
                        "testnet": data.get('testnet', 'unknown'),
                        "connected": data.get('connected', False),
                        "message": data.get('message', 'No message')
                    })
                    return True
                else:
                    self.log_test("Real Money Activation", False, f"Activation failed: {data.get('message', 'Unknown error')}")
                    return False
            else:
                self.log_test("Real Money Activation", False, f"HTTP {response.status_code}: {response.text}")
                return False
                
        except Exception as e:
            self.log_test("Real Money Activation", False, f"Request failed: {e}")
            return False
    
    def test_binance_connection(self):
        """Test Binance live connection"""
        try:
            response = requests.get(f"{self.base_url}/api/binance_status", timeout=10)
            if response.status_code == 200:
                data = response.json()
                
                connected = data.get('connected', False)
                testnet = data.get('testnet', True)
                btc_price = data.get('current_btc_price', 0)
                connection_test = data.get('connection_test', 'unknown')
                
                # Check if it's real money mode (testnet should be False)
                real_money_mode = not testnet
                
                self.log_test("Binance Connection", connected, 
                            f"Connection test: {connection_test}", {
                    "connected": connected,
                    "real_money_mode": real_money_mode,
                    "testnet": testnet,
                    "btc_price": f"${btc_price:,.2f}",
                    "connector_type": data.get('connector_type', 'unknown')
                })
                
                return connected and real_money_mode
            else:
                self.log_test("Binance Connection", False, f"HTTP {response.status_code}")
                return False
                
        except Exception as e:
            self.log_test("Binance Connection", False, f"Request failed: {e}")
            return False
    
    def test_account_balance(self):
        """Test account balance retrieval"""
        try:
            response = requests.get(f"{self.base_url}/api/binance_status", timeout=10)
            if response.status_code == 200:
                data = response.json()
                balance = data.get('account_balance')
                
                if balance:
                    self.log_test("Account Balance", True, "Balance retrieved successfully", {
                        "USDT": balance.get('USDT', 'N/A'),
                        "BTC": balance.get('BTC', 'N/A')
                    })
                    return True
                else:
                    self.log_test("Account Balance", False, "No balance data available")
                    return False
            else:
                self.log_test("Account Balance", False, f"HTTP {response.status_code}")
                return False
                
        except Exception as e:
            self.log_test("Account Balance", False, f"Request failed: {e}")
            return False
    
    def test_trading_status(self):
        """Test trading status in real money mode"""
        try:
            response = requests.get(f"{self.base_url}/api/trading_status", timeout=5)
            if response.status_code == 200:
                data = response.json()
                
                live_mode = data.get('is_live_mode', False)
                binance_connected = data.get('binance_connected', False)
                
                self.log_test("Trading Status", True, "Trading status retrieved", {
                    "live_mode": live_mode,
                    "binance_connected": binance_connected,
                    "current_price": f"${data.get('current_price', 0):,.2f}",
                    "balance": f"${data.get('balance', 0):,.2f}"
                })
                
                return live_mode and binance_connected
            else:
                self.log_test("Trading Status", False, f"HTTP {response.status_code}")
                return False
                
        except Exception as e:
            self.log_test("Trading Status", False, f"Request failed: {e}")
            return False
    
    def test_simulation_mode_toggle(self):
        """Test switching back to simulation mode"""
        try:
            response = requests.post(f"{self.base_url}/api/toggle_live_mode", 
                                   json={'live_mode': False}, 
                                   timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                if data.get('status') == 'success':
                    self.log_test("Simulation Mode", True, "Switched back to simulation", {
                        "live_mode": data.get('live_mode', True),
                        "message": data.get('message', 'No message')
                    })
                    return True
                else:
                    self.log_test("Simulation Mode", False, f"Switch failed: {data.get('message', 'Unknown error')}")
                    return False
            else:
                self.log_test("Simulation Mode", False, f"HTTP {response.status_code}")
                return False
                
        except Exception as e:
            self.log_test("Simulation Mode", False, f"Request failed: {e}")
            return False
    
    def run_comprehensive_test(self):
        """Run all tests"""
        print("🧪 REAL MONEY TRADING MODE COMPREHENSIVE TEST")
        print("=" * 60)
        print("⚠️ WARNING: This will test REAL MONEY trading connections")
        print("⚠️ No actual trades will be placed during testing")
        print("=" * 60)
        
        # Confirm with user
        user_confirm = input("\nContinue with real money mode testing? (y/N): ").lower()
        if user_confirm != 'y':
            print("❌ Test cancelled by user")
            return False
        
        print(f"\n🚀 Starting tests at {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("-" * 40)
        
        # Run tests in sequence
        tests_passed = 0
        total_tests = 6
        
        if self.test_webapp_running():
            tests_passed += 1
        
        if self.test_real_money_toggle():
            tests_passed += 1
            time.sleep(2)  # Allow connection to establish
        
        if self.test_binance_connection():
            tests_passed += 1
        
        if self.test_account_balance():
            tests_passed += 1
        
        if self.test_trading_status():
            tests_passed += 1
        
        if self.test_simulation_mode_toggle():
            tests_passed += 1
        
        # Summary
        print("\n" + "=" * 60)
        print("📊 TEST SUMMARY")
        print("=" * 60)
        print(f"✅ Tests Passed: {tests_passed}/{total_tests}")
        print(f"❌ Tests Failed: {total_tests - tests_passed}/{total_tests}")
        
        success_rate = (tests_passed / total_tests) * 100
        print(f"📈 Success Rate: {success_rate:.1f}%")
        
        if tests_passed == total_tests:
            print("\n🎉 ALL TESTS PASSED!")
            print("✅ Real money trading mode is fully functional")
            print("💰 System ready for live trading with real funds")
        else:
            print(f"\n⚠️ {total_tests - tests_passed} TESTS FAILED")
            print("🔍 Check the failed tests above for details")
        
        print(f"\n🌐 Dashboard URL: {self.base_url}")
        print("💡 Use the web interface to manually test the toggle functionality")
        print("=" * 60)
        
        return tests_passed == total_tests

if __name__ == "__main__":
    tester = RealMoneyTradingTester()
    success = tester.run_comprehensive_test()
    exit(0 if success else 1)
